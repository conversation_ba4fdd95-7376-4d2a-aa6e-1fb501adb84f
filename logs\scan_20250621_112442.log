2025-06-21 11:24:43,044 - DEBUG - Started periodic cleanup thread
2025-06-21 11:24:44,077 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-06-21 11:24:44,115 - INFO - win32com configured. DOC parsing support enabled.
2025-06-21 11:24:44,115 - DEBUG - antiword not available as fallback for DOC files.
2025-06-21 11:24:44,351 - INFO - Application started with refactored architecture
2025-06-21 11:24:44,384 - DEBUG - Could not set window icon: bitmap "C:\Users\<USER>\Desktop\L457\SearchTools-v1\searchtools\ui\..\..\icon.ico" not defined
2025-06-21 11:24:44,439 - DEBUG - Could not load image logo: Logo file not found
2025-06-21 11:24:46,124 - ERROR - Error applying UI styling [function=apply_styling, args_count=1, kwargs_keys=[]]
2025-06-21 11:24:46,130 - DEBUG - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\L457\SearchTools-v1\searchtools\utils\error_handling.py", line 571, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\L457\SearchTools-v1\searchtools\ui\components\ui_manager.py", line 39, in apply_styling
    self._style_progressbar()
  File "C:\Users\<USER>\Desktop\L457\SearchTools-v1\searchtools\ui\components\ui_manager.py", line 98, in _style_progressbar
    background=config.UI_COLORS["primary_color"],
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
KeyError: 'primary_color'

2025-06-21 11:24:46,131 - INFO - MainWindow initialized with component-based architecture
