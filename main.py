"""
Main entry point for the SearchTools application
"""

import tkinter as tk
from searchtools.ui.main_window_refactored import Main<PERSON>indow
from searchtools.utils.logging import logger

# We'll use the standard tkinter without external theme packages

def main():
    """
    Main entry point for the application.
    """
    try:
        # Set up the root window
        root = tk.Tk()

        # Log application start
        logger.info("Application started with refactored architecture")

        # Create the main window (refactored version handles icon setting internally)
        MainWindow(root)  # No need to store the reference as it's attached to root

        # Start the main loop
        root.mainloop()

    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        import traceback
        logger.debug(traceback.format_exc())
    finally:
        # Log application exit
        logger.info("Application exited")

if __name__ == "__main__":
    main()
