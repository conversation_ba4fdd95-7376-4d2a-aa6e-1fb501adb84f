["tests/test_phase2_components.py::TestProgressTracker::test_get_progress_state", "tests/test_phase2_components.py::TestProgressTracker::test_initialization", "tests/test_phase2_components.py::TestProgressTracker::test_reset_progress", "tests/test_phase2_components.py::TestProgressTracker::test_update_file_progress", "tests/test_phase2_components.py::TestProgressTracker::test_update_overall_progress", "tests/test_phase2_components.py::TestProgressTracker::test_update_status", "tests/test_phase2_components.py::TestResultFormatter::test_clean_context", "tests/test_phase2_components.py::TestResultFormatter::test_format_file_path_relative", "tests/test_phase2_components.py::TestResultFormatter::test_highlight_keyword", "tests/test_phase2_components.py::TestResultFormatter::test_truncate_context", "tests/test_phase2_components.py::TestResultManager::test_add_result", "tests/test_phase2_components.py::TestResultManager::test_clear_results", "tests/test_phase2_components.py::TestResultManager::test_get_results_count", "tests/test_phase2_components.py::TestResultManager::test_initialization", "tests/test_phase2_components.py::TestScanController::test_initialization", "tests/test_phase2_components.py::TestScanController::test_parse_keywords", "tests/test_phase2_components.py::TestScanController::test_start_scan_success", "tests/test_phase2_components.py::TestScanController::test_stop_scan", "tests/test_phase2_components.py::TestScanController::test_validate_scan_inputs_empty_folder", "tests/test_phase2_components.py::TestScanController::test_validate_scan_inputs_empty_keywords", "tests/test_phase2_components.py::TestSearchResultFactory::test_create_archive_result", "tests/test_phase2_components.py::TestSearchResultFactory::test_create_batch_results", "tests/test_phase2_components.py::TestSearchResultFactory::test_create_binary_result", "tests/test_phase2_components.py::TestSearchResultFactory::test_create_csv_result", "tests/test_phase2_components.py::TestSearchResultFactory::test_create_json_result", "tests/test_phase2_components.py::TestSearchResultFactory::test_create_text_result", "tests/test_phase2_components.py::TestSearchResultFactory::test_create_xml_result", "tests/test_phase2_components.py::TestThreadSafeProgressTracker::test_thread_safe_updates"]