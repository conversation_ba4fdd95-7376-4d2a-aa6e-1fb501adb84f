"""
Core scanning functionality
"""

import os
import threading
import concurrent.futures
import queue
import time
from typing import List, Optional, Callable

from searchtools.utils.logging import logger
from searchtools.utils.logging_guidelines import (
    log_parser_error, log_file_error, log_search_operation
)
from searchtools.utils.file_utils import get_file_extension, cleanup_temp_dir
from searchtools.utils.temp_manager import temp_manager
from searchtools.core.result import SearchResult
from searchtools.parsers.plugin_manager import plugin_manager
from searchtools.parsers.archive_parser import ArchiveParser
from searchtools.utils.events import Event, EventType, event_dispatcher
import config

class Scanner:
    """
    Main scanner class for searching files.
    """

    def __init__(self):
        """
        Initialize the scanner.
        """
        # Thread-safe shared state
        self._state_lock = threading.RLock()
        self._progress_lock = threading.RLock()
        self._results_lock = threading.RLock()

        # Shared state variables (protected by locks)
        self.results: List[SearchResult] = []
        self.scan_in_progress: bool = False
        self.stop_scan: bool = False
        self.temp_dirs: List[str] = []
        self.current_file_name: str = ""  # Track current file being processed

        # Callbacks (thread-safe by nature as they're only set once)
        self.progress_callback: Optional[Callable[[int], None]] = None
        self.status_callback: Optional[Callable[[str], None]] = None
        self.result_callback: Optional[Callable[[SearchResult], None]] = None
        self.file_progress_callback: Optional[Callable[[int, str], None]] = None  # New callback for file-level progress

        # Search parameters (protected by state lock)
        self.current_keywords: List[str] = []
        self.current_case_sensitive: bool = False
        self.current_use_regex: bool = False
        self.current_whole_word: bool = False

    def _set_scan_state(self, in_progress: bool, stop_scan: bool = None):
        """
        Thread-safe method to update scan state.

        Args:
            in_progress (bool): Whether scan is in progress
            stop_scan (bool, optional): Whether to stop scan
        """
        with self._state_lock:
            self.scan_in_progress = in_progress
            if stop_scan is not None:
                self.stop_scan = stop_scan

    def _get_scan_state(self):
        """
        Thread-safe method to get current scan state.

        Returns:
            tuple: (scan_in_progress, stop_scan)
        """
        with self._state_lock:
            return self.scan_in_progress, self.stop_scan

    def _set_current_file(self, file_name: str):
        """
        Thread-safe method to update current file being processed.

        Args:
            file_name (str): Name of the current file
        """
        with self._state_lock:
            self.current_file_name = file_name

    def _get_current_file(self):
        """
        Thread-safe method to get current file being processed.

        Returns:
            str: Name of the current file
        """
        with self._state_lock:
            return self.current_file_name

    def _add_result(self, result: SearchResult):
        """
        Thread-safe method to add a result.

        Args:
            result (SearchResult): Result to add
        """
        with self._results_lock:
            self.results.append(result)

    def _get_results_copy(self):
        """
        Thread-safe method to get a copy of current results.

        Returns:
            List[SearchResult]: Copy of current results
        """
        with self._results_lock:
            return self.results.copy()

    def _clear_results(self):
        """
        Thread-safe method to clear results.
        """
        with self._results_lock:
            self.results.clear()

    @property
    def keywords(self):
        """
        Get the current keywords.

        Returns:
            list: Current keywords
        """
        return self.current_keywords

    def set_callbacks(self, progress_callback=None, status_callback=None, result_callback=None, file_progress_callback=None):
        """
        Set callbacks for progress, status, and results.

        Args:
            progress_callback (callable, optional): Callback for overall progress updates. Defaults to None.
            status_callback (callable, optional): Callback for status updates. Defaults to None.
            result_callback (callable, optional): Callback for result updates. Defaults to None.
            file_progress_callback (callable, optional): Callback for file-level progress updates. Defaults to None.
        """
        self.progress_callback = progress_callback
        self.status_callback = status_callback
        self.result_callback = result_callback
        self.file_progress_callback = file_progress_callback

    def start_scan(self, path, keywords, case_sensitive=False, use_regex=False, whole_word=False):
        """
        Start scanning a directory or file for keywords.

        Args:
            path (str): Path to scan (can be a directory or a file)
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.
        """
        # Clear previous results & cleanup old temp dirs (thread-safe)
        self._clear_results()
        self.cleanup_temp_dirs()

        # Set scan state (thread-safe)
        self._set_scan_state(in_progress=True, stop_scan=False)

        # Check if path is a file or directory
        is_file = os.path.isfile(path)

        # Log scan start
        if is_file:
            log_search_operation("started", file_path=path)
        else:
            log_search_operation("started", folder_path=path)

        logger.info(f"Keywords: {', '.join(keywords)}")
        logger.info(f"Case sensitive: {case_sensitive}")

        # Update status
        if self.status_callback:
            if is_file:
                self.status_callback(f"Initializing scan of file: {os.path.basename(path)}...")
            else:
                self.status_callback("Initializing scan...")

        if self.progress_callback:
            self.progress_callback(0)

        # Store search parameters for archive processing
        self.current_keywords = keywords
        self.current_case_sensitive = case_sensitive
        self.current_use_regex = use_regex
        self.current_whole_word = whole_word

        # Start scanning in a separate thread
        if is_file:
            # For a single file, use a simpler approach
            scan_thread = threading.Thread(
                target=self._scan_single_file,
                args=(path, keywords, case_sensitive, use_regex, whole_word)
            )
        else:
            # For a directory, use the regular scan_directory method
            scan_thread = threading.Thread(
                target=self.scan_directory,
                args=(path, keywords, case_sensitive, use_regex, whole_word)
            )

        scan_thread.daemon = True
        scan_thread.start()

    def stop_scanning(self):
        """
        Stop the current scan.

        This method sets the stop_scan flag and updates the UI to indicate
        that the scan is being stopped. The actual stopping of the scan
        happens asynchronously as the scanner threads check the stop_scan flag.
        """
        scan_in_progress, _ = self._get_scan_state()
        if scan_in_progress:
            # Set the stop flag (thread-safe)
            with self._state_lock:
                self.stop_scan = True

            # Update status with current file information if available
            current_file = self._get_current_file()
            if self.status_callback:
                if current_file:
                    self.status_callback(f"Stopping scan... Finishing {current_file}...")
                else:
                    self.status_callback("Stopping scan...")

            logger.info(f"Stop scan requested by user while processing {current_file or 'unknown file'}")

            # Start a thread to finalize the scan if it doesn't stop quickly
            threading.Thread(
                target=self._ensure_scan_stops,
                daemon=True
            ).start()

    def _ensure_scan_stops(self):
        """
        Ensure that the scan stops within a reasonable time.

        This method is called in a separate thread when stop_scanning is called.
        It waits for a timeout period and then forces the scan to stop if it's
        still in progress.
        """
        # Wait for a reasonable time for the scan to stop naturally
        timeout = config.STOP_SCAN_TIMEOUT if hasattr(config, 'STOP_SCAN_TIMEOUT') else 5.0
        start_time = time.time()

        # Update status periodically during the stopping process
        update_interval = 1.0  # Update status every second
        last_update = start_time

        # Track if we've seen no progress for too long
        last_file_name = self._get_current_file()
        stuck_time = start_time
        max_stuck_time = 5.0  # Force stop if stuck on same file for 5 seconds (increased for large files)

        scan_in_progress, _ = self._get_scan_state()
        while scan_in_progress and time.time() - start_time < timeout:
            # Sleep a short interval
            time.sleep(config.TIMEOUT_SLEEP_INTERVAL)

            # Check if we're stuck on the same file (thread-safe)
            current_file = self._get_current_file()
            if current_file == last_file_name:
                # Check if the current file is a large file and adjust timeout accordingly
                current_stuck_time = max_stuck_time
                try:
                    if current_file and os.path.exists(current_file):
                        file_size = os.path.getsize(current_file)
                        if file_size > config.LARGE_FILE_SIZE * 5:  # Extra large file (500MB+)
                            current_stuck_time = max_stuck_time * 3  # Give extra large files more time
                            logger.info(f"Allowing extra time for large file {current_file} ({file_size / (1024*1024):.2f} MB)")
                except Exception as e:
                    logger.debug(f"Error checking file size during stuck detection: {e}")

                # Check if we've been stuck too long
                if time.time() - stuck_time > current_stuck_time:
                    logger.warning(f"Scan appears stuck on file {current_file} for {current_stuck_time} seconds, forcing stop")
                    break
            else:
                # File changed, reset stuck timer
                last_file_name = current_file
                stuck_time = time.time()

            # Update scan state for next iteration
            scan_in_progress, _ = self._get_scan_state()

            # Update status periodically
            current_time = time.time()
            if current_time - last_update >= update_interval:
                if self.status_callback:
                    remaining = max(0, int(timeout - (current_time - start_time)))
                    self.status_callback(f"Stopping scan... Waiting for processes to finish ({remaining}s)...")
                last_update = current_time

        # If the scan is still in progress, force it to stop (thread-safe check)
        scan_in_progress, _ = self._get_scan_state()
        if scan_in_progress:
            logger.warning("Scan did not stop within timeout period, forcing stop")
            if self.status_callback:
                self.status_callback("Scan taking too long to stop. Forcing termination...")

            # Force cleanup of any resources
            try:
                # Try to clean up any active threads or processes
                self._force_cleanup()
            except Exception as e:
                logger.error(f"Error during forced cleanup: {e}")

            # Finalize the scan
            self._finalize_scan(stopped=True)

    def _force_cleanup(self):
        """
        Force cleanup of any resources that might be preventing the scan from stopping.
        This is a last resort method called when the scan doesn't stop naturally.
        """
        logger.info("Performing forced cleanup of scanner resources")

        # Clear any queues that might be full
        try:
            # Clean up temp directories immediately
            self.cleanup_temp_dirs()
        except Exception as e:
            logger.error(f"Error cleaning temp dirs during forced stop: {e}")

        # Log the current file that might be causing the issue (thread-safe)
        current_file = self._get_current_file()
        if current_file:
            logger.warning(f"Scan appears to be stuck on file: {current_file}")
            try:
                if os.path.exists(current_file):
                    file_size = os.path.getsize(current_file)
                    logger.warning(f"Stuck file size: {file_size / (1024*1024):.2f} MB")
            except Exception as e:
                logger.error(f"Error checking stuck file details: {e}")

        # Reset the current file name and set scan as not in progress (thread-safe)
        self._set_current_file("")
        self._set_scan_state(in_progress=False)

        # Log completion of forced cleanup
        logger.info("Forced cleanup completed")

    def cleanup_temp_dirs(self):
        """
        Remove all created temporary directories using secure cleanup.
        """
        # Clean up directories managed by the legacy system
        for temp_dir in self.temp_dirs:
            cleanup_temp_dir(temp_dir)
        self.temp_dirs.clear()

        # Also clean up any directories managed by the secure temp manager
        # This ensures comprehensive cleanup even if some directories were created
        # through different paths
        try:
            cleaned_count = temp_manager.cleanup_all()
            if cleaned_count > 0:
                logger.debug(f"Secure temp manager cleaned up {cleaned_count} additional directories")
        except Exception as e:
            logger.warning(f"Error during secure temp directory cleanup: {e}")

    def _estimate_items(self, root_folder):
        """
        Estimate the number of items to process for progress tracking.

        Args:
            root_folder (str): Root folder to scan

        Returns:
            int: Estimated number of items to process
        """
        logger.info("Estimating items to process...")
        estimated_total_items = 0

        try:
            for _, _, files in os.walk(root_folder):
                estimated_total_items += len(files)
                # Add a rough estimate for archives
                estimated_total_items += sum(config.ESTIMATED_FILES_PER_ARCHIVE for f in files
                                           if f.lower().endswith(config.ARCHIVE_EXTENSIONS))
        except Exception as e:
            logger.error(f"Error estimating items: {e}")
            estimated_total_items = config.DEFAULT_ESTIMATION_FALLBACK  # Default value if estimation fails

        return estimated_total_items

    def _collect_files(self, root_folder):
        """
        Collect all files to process from the root folder.

        Args:
            root_folder (str): Root folder to scan

        Returns:
            list: List of file paths to process
        """
        files_to_process = []
        for root, _, files in os.walk(root_folder):
            for file_name in files:
                files_to_process.append(os.path.join(root, file_name))
        return files_to_process

    def _update_progress(self, processed_items, total_items, item_path):
        """
        Update progress indicators.

        Args:
            processed_items (int): Number of processed items
            total_items (int): Total number of items
            item_path (str): Path of the current item
        """
        # Update progress bar
        progress = (processed_items / total_items) * config.PROGRESS_COMPLETE

        # Dispatch progress update event
        event_dispatcher.dispatch(Event(
            EventType.PROGRESS_UPDATE,
            {
                "progress": progress,
                "processed_items": processed_items,
                "total_items": total_items
            }
        ))

        # Also call the legacy callback for backward compatibility
        if self.progress_callback:
            self.progress_callback(progress)

        # Update status bar every N files or on last file
        if processed_items % config.STATUS_UPDATE_FREQUENCY == 0 or processed_items == total_items:
            status_message = f"Scanning: {os.path.basename(item_path)} ({processed_items}/{total_items})"

            # Dispatch status update event
            event_dispatcher.dispatch(Event(
                EventType.STATUS_UPDATE,
                {"status": status_message}
            ))

            # Also call the legacy callback for backward compatibility
            if self.status_callback:
                self.status_callback(status_message)

    def _process_result_queue(self, result_queue):
        """
        Process results from the queue.

        Args:
            result_queue (Queue): Queue containing results
        """
        # Collect results in a batch for more efficient processing
        results_batch = []

        # Get all available results from the queue
        while not result_queue.empty():
            try:
                result = result_queue.get_nowait()
                results_batch.append(result)
            except queue.Empty:
                break

        # If no results, nothing to do
        if not results_batch:
            return

        # Process all results in the batch
        for result in results_batch:
            # Add the result to the scanner's results list and trigger the callback
            self.add_result(result)

        # No need to force UI updates here - the UI will handle batched updates
        # through the throttling mechanism in MainWindow._schedule_ui_update

    def _scan_single_file(self, file_path, keywords, case_sensitive, use_regex=False, whole_word=False):
        """
        Scan a single file for keywords.

        Args:
            file_path (str): Path to the file
            keywords (list): List of keywords to search for
            case_sensitive (bool): Whether to use case-sensitive search
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.
        """
        # Initialize scan
        if self.status_callback:
            self.status_callback(f"Scanning file: {os.path.basename(file_path)}")

        # Create a result queue for this file
        result_queue = queue.Queue()

        try:
            # Process the file
            self.process_file_in_thread(
                file_path,
                os.path.basename(file_path),
                keywords,
                case_sensitive,
                result_queue,
                use_regex,
                whole_word
            )

            # Process results from the queue
            self._process_result_queue(result_queue)

            # Set progress to 100%
            if self.progress_callback:
                self.progress_callback(100)

        except Exception as e:
            logger.error(f"Error scanning file {file_path}: {e}")
            import traceback
            logger.debug(f"Traceback: {traceback.format_exc()}")

        # Finalize the scan
        self._finalize_scan(stopped=self.stop_scan)

    def _finalize_scan(self, stopped=False):
        """
        Finalize the scan and update UI.

        Args:
            stopped (bool): Whether the scan was stopped by the user
        """
        # Process any remaining results in queues before finalizing
        # This ensures all results are displayed before showing "complete" message
        try:
            # Small delay to allow any pending results to be processed
            time.sleep(0.2)

            # Force a final UI update by calling the result callback's update method if available
            if self.result_callback and hasattr(self.result_callback, "__self__"):
                try:
                    # Check if the result callback has the _process_result_queue method
                    if hasattr(self.result_callback.__self__, "_process_result_queue"):
                        # Call the method to process any queued results
                        self.result_callback.__self__._process_result_queue()
                except Exception as e:
                    logger.debug(f"Error during final UI update: {e}")
        except Exception as e:
            logger.debug(f"Error during final result processing: {e}")

        # Now mark the scan as complete (thread-safe)
        self._set_scan_state(in_progress=False)

        status = 'stopped' if stopped else 'complete'
        results_copy = self._get_results_copy()
        match_count = len(results_copy)

        # Create a more detailed status message
        if stopped:
            status_message = f"Scan stopped by user. Found {match_count} matches before stopping."
        else:
            status_message = f"Scan complete. Found {match_count} matches."

        # Clear the current file name (thread-safe)
        self._set_current_file("")

        # Log the operation completion
        log_search_operation("finished", status=status, match_count=match_count)

        if self.status_callback:
            self.status_callback(status_message)

    def scan_directory(self, root_folder, keywords, case_sensitive, use_regex=False, whole_word=False):
        """
        Scan a directory for keywords.

        Args:
            root_folder (str): Root folder to scan
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.
        """
        # Initialize scan
        if not self._initialize_scan(root_folder):
            return

        # Collect files to process
        files_to_process = self._collect_files(root_folder)

        # Process files using thread pool
        self._process_files_with_threadpool(
            files_to_process, keywords, case_sensitive, use_regex, whole_word
        )

        # Finalize the scan
        self._finalize_scan(stopped=self.stop_scan)

    def _initialize_scan(self, root_folder):
        """
        Initialize the scan process.

        Args:
            root_folder (str): Root folder to scan

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        # Estimate items to process
        estimated_total_items = self._estimate_items(root_folder)

        # Check if there are any files to process
        if estimated_total_items == 0:
            logger.warning("No items found in the selected directory.")
            if self.status_callback:
                self.status_callback("Scan complete. No files found.")
            self._set_scan_state(in_progress=False)
            return False

        # Log and update status
        logger.info(f"Estimated {estimated_total_items} items to process")
        if self.status_callback:
            self.status_callback("Scanning...")

        return True

    def _process_files_with_threadpool(self, files_to_process, keywords, case_sensitive, use_regex, whole_word):
        """
        Process files using a thread pool executor.

        Args:
            files_to_process (list): List of file paths to process
            keywords (list): List of keywords to search for
            case_sensitive (bool): Whether to use case-sensitive search
            use_regex (bool): Whether to treat keywords as regular expressions
            whole_word (bool): Whether to match whole words only
        """
        # Set up thread pool parameters
        thread_pool_params = self._setup_thread_pool_params(files_to_process)

        # Process files in batches using thread pool
        self._execute_batch_processing(
            files_to_process,
            keywords,
            case_sensitive,
            use_regex,
            whole_word,
            thread_pool_params
        )

    def _setup_thread_pool_params(self, files_to_process):
        """
        Set up parameters for thread pool execution.

        Args:
            files_to_process (list): List of file paths to process

        Returns:
            dict: Dictionary containing thread pool parameters
        """
        return {
            'max_workers': config.MAX_WORKERS,
            'result_queue': queue.Queue(),
            'processed_items': 0,
            'batch_size': config.BATCH_SIZE,
            'total_files': len(files_to_process)
        }

    def _execute_batch_processing(self, files_to_process, keywords, case_sensitive,
                                 use_regex, whole_word, thread_pool_params):
        """
        Execute batch processing of files using thread pool.

        Args:
            files_to_process (list): List of file paths to process
            keywords (list): List of keywords to search for
            case_sensitive (bool): Whether to use case-sensitive search
            use_regex (bool): Whether to treat keywords as regular expressions
            whole_word (bool): Whether to match whole words only
            thread_pool_params (dict): Thread pool parameters
        """
        # Extract parameters
        max_workers = thread_pool_params['max_workers']
        result_queue = thread_pool_params['result_queue']
        processed_items = thread_pool_params['processed_items']
        batch_size = thread_pool_params['batch_size']
        total_files = thread_pool_params['total_files']

        # Log start of processing
        logger.info(f"Processing {total_files} files with {max_workers} workers in batches of {batch_size}")

        # Process files in batches
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {}

            # Process files in batches
            for i in range(0, total_files, batch_size):
                _, stop_scan = self._get_scan_state()
                if stop_scan:
                    logger.info("Scan stopped by user during batch processing")
                    break

                # Process the current batch
                processed_items = self._process_file_batch(
                    files_to_process[i:i+batch_size],
                    executor,
                    futures,
                    keywords,
                    case_sensitive,
                    result_queue,
                    use_regex,
                    whole_word,
                    processed_items,
                    total_files
                )

        # Process any remaining results
        self._process_result_queue(result_queue)

    def _process_file_batch(self, batch, executor, futures, keywords, case_sensitive,
                           result_queue, use_regex, whole_word, processed_items, total_files):
        """
        Process a batch of files.

        Args:
            batch (list): Batch of file paths to process
            executor (ThreadPoolExecutor): Thread pool executor
            futures (dict): Dictionary to track futures
            keywords (list): List of keywords to search for
            case_sensitive (bool): Whether to use case-sensitive search
            result_queue (Queue): Queue for results
            use_regex (bool): Whether to treat keywords as regular expressions
            whole_word (bool): Whether to match whole words only
            processed_items (int): Number of items processed so far
            total_files (int): Total number of files to process

        Returns:
            int: Updated count of processed items
        """
        # Submit tasks for all files in the batch
        batch_futures = self._submit_batch_tasks(
            batch, executor, futures, keywords, case_sensitive,
            result_queue, use_regex, whole_word
        )

        # Process results from completed futures
        processed_items = self._process_batch_results(
            batch_futures, futures, result_queue, processed_items, total_files
        )

        return processed_items

    def _submit_batch_tasks(self, batch, executor, futures, keywords, case_sensitive,
                           result_queue, use_regex, whole_word):
        """
        Submit tasks for all files in a batch to the thread pool.

        Args:
            batch (list): Batch of file paths to process
            executor (ThreadPoolExecutor): Thread pool executor
            futures (dict): Dictionary to track futures
            keywords (list): List of keywords to search for
            case_sensitive (bool): Whether to use case-sensitive search
            result_queue (Queue): Queue for results
            use_regex (bool): Whether to treat keywords as regular expressions
            whole_word (bool): Whether to match whole words only

        Returns:
            list: List of futures for the submitted tasks
        """
        batch_futures = []

        for item_path in batch:
            _, stop_scan = self._get_scan_state()
            if stop_scan:
                break

            # Skip directories - os.walk gave us files only
            if not os.path.isfile(item_path):
                continue

            # Submit the file processing task
            future = executor.submit(
                self.process_file_in_thread,
                item_path,
                os.path.basename(item_path),
                keywords,
                case_sensitive,
                result_queue,
                use_regex,
                whole_word
            )
            futures[future] = item_path
            batch_futures.append(future)

            # Check for results after each file to ensure real-time updates
            self._process_result_queue(result_queue)

        return batch_futures

    def _process_batch_results(self, batch_futures, futures, result_queue, processed_items, total_files):
        """
        Process results from completed futures.

        Args:
            batch_futures (list): List of futures for the submitted tasks
            futures (dict): Dictionary mapping futures to file paths
            result_queue (Queue): Queue for results
            processed_items (int): Number of items processed so far
            total_files (int): Total number of files to process

        Returns:
            int: Updated count of processed items
        """
        # Use a timeout to allow checking the stop_scan flag periodically
        timeout = 0.1  # 100ms timeout to check stop_scan flag frequently

        # Create a copy of batch_futures to avoid modification during iteration
        pending = list(batch_futures)

        while pending and not self.stop_scan:
            # Wait for some futures to complete with a timeout
            done, pending = concurrent.futures.wait(
                pending,
                timeout=timeout,
                return_when=concurrent.futures.FIRST_COMPLETED
            )

            # Process completed futures
            for future in done:
                if self.stop_scan:
                    break

                processed_items += 1
                item_path = futures[future]

                # Get results and handle exceptions
                try:
                    future.result()
                except Exception as e:
                    self._handle_file_processing_error(item_path, e)

                # Process any results in the queue immediately
                self._process_result_queue(result_queue)

                # Update progress
                self._update_progress(processed_items, total_files, item_path)

            # Process any results that might have been added while waiting
            self._process_result_queue(result_queue)

        # If stopped, cancel any pending futures
        if self.stop_scan and pending:
            logger.info(f"Cancelling {len(pending)} pending file processing tasks")
            for future in pending:
                future.cancel()

        return processed_items

    def _handle_file_processing_error(self, item_path, error):
        """
        Handle errors that occur during file processing.

        Args:
            item_path (str): Path to the file that caused the error
            error (Exception): The exception that occurred
        """
        log_file_error("read_error", item_path, error)
        import traceback
        logger.debug(f"Traceback: {traceback.format_exc()}")

    def _update_file_progress(self, progress, file_name):
        """
        Update the file progress indicator.

        Args:
            progress (int): Progress percentage (0-100)
            file_name (str): Name of the file being processed
        """
        # Dispatch file progress update event
        event_dispatcher.dispatch(Event(
            EventType.FILE_PROGRESS_UPDATE,
            {
                "progress": progress,
                "file_name": file_name
            }
        ))

        # Also call the legacy callback for backward compatibility
        if self.file_progress_callback:
            self.file_progress_callback(progress, file_name)

    def _mark_archive_for_processing(self, file_path, file_name, result_queue):
        """
        Mark an archive file for processing by the main thread.

        Args:
            file_path (str): Path to the archive file
            file_name (str): Name of the archive file
            result_queue (queue.Queue): Queue for results
        """
        # Update file progress
        self._update_file_progress(config.PROGRESS_ARCHIVE_MARKING, file_name)

        # Mark for main thread handling
        result_queue.put(
            SearchResult(
                file=file_name,
                path=file_path,
                keyword=config.ARCHIVE_MARKER_KEYWORD,
                context=f"Archive will be processed by main thread: {file_name}",
                location="Archive",
                full_context="Archive marker"
            )
        )

        # Update file progress to complete
        self._update_file_progress(config.PROGRESS_COMPLETE, file_name)

    def _process_regular_file(self, file_path, file_name, keywords, case_sensitive,
                             result_queue, use_regex, whole_word):
        """
        Process a regular (non-archive) file.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            case_sensitive (bool): Whether to use case-sensitive search
            result_queue (queue.Queue): Queue for results
            use_regex (bool): Whether to treat keywords as regular expressions
            whole_word (bool): Whether to match whole words only
        """
        # Check if scan has been stopped
        if self.stop_scan:
            logger.info(f"Skipping file {file_name} as scan was stopped")
            return

        # Initialize the plugin manager if needed
        plugin_manager.discover_parsers()
        self._update_file_progress(config.PROGRESS_FILE_INIT, file_name)

        # Check if scan has been stopped
        if self.stop_scan:
            logger.info(f"Stopping after initialization of {file_name}")
            return

        # Check file size for potential timeout issues
        try:
            file_size = os.path.getsize(file_path)
            if file_size > config.LARGE_FILE_SIZE * 5:  # Extra large file (500MB+)
                logger.warning(f"Processing extremely large file: {file_name} ({file_size / (1024*1024):.2f} MB)")
                if self.status_callback:
                    self.status_callback(f"Processing extremely large file: {file_name} ({file_size / (1024*1024):.2f} MB)")
        except Exception as e:
            logger.error(f"Error checking file size for {file_name}: {e}")

        # Get the appropriate parser for this file
        parser_class = plugin_manager.get_parser_for_file(file_path)
        self._update_file_progress(config.PROGRESS_PARSER_FOUND, file_name)

        # Process the file with the appropriate parser
        results = []
        if parser_class and not self.stop_scan:
            # Use the parser
            logger.debug(f"Using {parser_class.get_name()} for {file_name}")
            self._update_file_progress(config.PROGRESS_PARSING_STARTED, file_name)

            try:
                # Parse the file with periodic stop checks
                results = parser_class.parse(file_path, file_name, keywords, case_sensitive, use_regex, whole_word)

                # Check if scan was stopped during parsing
                if self.stop_scan:
                    logger.info(f"Scan stopped during parsing of {file_name}")
                    return

            except Exception as e:
                if self.stop_scan:
                    logger.info(f"Exception during parsing of {file_name} after stop was requested: {e}")
                else:
                    logger.error(f"Error parsing {file_name}: {e}")
                return

            # Add results to the queue and process them immediately
            for i, result in enumerate(results):
                # Check if scan has been stopped
                if self.stop_scan:
                    logger.info(f"Scan stopped while processing results from {file_name}")
                    break

                # Add result to queue
                result_queue.put(result)

                # Process each result immediately for real-time UI updates
                # This ensures users see results as they're found
                self._process_result_queue(result_queue)

                # Periodically check for stop requests and update status during result processing
                if i > 0 and i % 100 == 0:
                    if self.status_callback and not self.stop_scan:
                        self.status_callback(f"Processing results from {file_name}: {i}/{len(results)}")
        else:
            if self.stop_scan:
                logger.info(f"Skipping parsing of {file_name} as scan was stopped")
            else:
                # No parser found, log warning
                logger.warning(f"No parser found for {file_name}")

        # Only update progress if not stopped
        if not self.stop_scan:
            # Update progress
            self._update_file_progress(config.PROGRESS_PARSING_COMPLETE, file_name)

            # Update file progress to complete
            self._update_file_progress(config.PROGRESS_COMPLETE, file_name)

    def process_file_in_thread(self, file_path, file_name, keywords, case_sensitive, result_queue, use_regex=False, whole_word=False):
        """
        Process a file in a worker thread, putting results in the queue.

        Args:
            file_path (str): Path to the file
            file_name (str): Name of the file
            keywords (list): List of keywords to search for
            case_sensitive (bool, optional): Whether to use case-sensitive search. Defaults to False.
            result_queue (queue.Queue): Queue for results
            use_regex (bool, optional): Whether to treat keywords as regular expressions. Defaults to False.
            whole_word (bool, optional): Whether to match whole words only. Defaults to False.
        """
        # Check if scan has been stopped (thread-safe)
        _, stop_scan = self._get_scan_state()
        if stop_scan:
            logger.info(f"Skipping file {file_name} as scan was stopped")
            return

        # Update the current file name for status updates (thread-safe)
        self._set_current_file(file_name)

        # Check file size and log warning for very large files
        try:
            file_size = os.path.getsize(file_path)
            if file_size > config.LARGE_FILE_SIZE:
                logger.warning(f"Processing large file: {file_name} ({file_size / (1024*1024):.2f} MB)")

                # For very large files, periodically check if stop was requested
                if self.status_callback:
                    self.status_callback(f"Processing large file: {file_name} ({file_size / (1024*1024):.2f} MB)")
        except Exception as e:
            logger.error(f"Error checking file size for {file_name}: {e}")

        # Initialize file processing
        self._init_file_processing(file_name)

        try:
            # Check if scan has been stopped
            if self.stop_scan:
                logger.info(f"Skipping processing of {file_name} as scan was stopped")
                return

            # Determine file type and process accordingly
            if ArchiveParser.is_archive(file_path) and not self.stop_scan:
                self._mark_archive_for_processing(file_path, file_name, result_queue)
            elif not self.stop_scan:
                self._process_regular_file(
                    file_path, file_name, keywords, case_sensitive,
                    result_queue, use_regex, whole_word
                )
        except Exception as e:
            if not self.stop_scan:  # Only log errors if not stopped
                self._handle_file_thread_error(file_name, e)

    def _init_file_processing(self, file_name):
        """
        Initialize file processing by updating status and progress.

        Args:
            file_name (str): Name of the file being processed
        """
        # Update current file name for status display
        self.current_file_name = file_name

        # Send file progress update - starting at 0%
        self._update_file_progress(config.PROGRESS_START, file_name)

    def _handle_file_thread_error(self, file_name, error):
        """
        Handle errors that occur during file processing in a thread.

        Args:
            file_name (str): Name of the file that caused the error
            error (Exception): The exception that occurred
        """
        log_parser_error("Thread", file_name, error)
        # Update file progress to complete even on error (to avoid stuck progress bar)
        self._update_file_progress(config.PROGRESS_COMPLETE, file_name)

    def _get_scan_callback(self):
        """
        Create a callback function for scanning extracted archive contents.

        Returns:
            callable: Callback function for scanning extracted archive contents
        """
        return lambda temp_dir: self.scan_directory(
            temp_dir,
            self.current_keywords,
            self.current_case_sensitive,
            self.current_use_regex,
            self.current_whole_word
        )

    def _process_zip_archive(self, file_path, file_name):
        """
        Process a ZIP archive.

        Args:
            file_path (str): Path to the ZIP archive
            file_name (str): Name of the ZIP archive

        Returns:
            bool: True if successful, False otherwise
        """
        self._update_file_progress(config.PROGRESS_ARCHIVE_EXTRACTION, file_name)
        return ArchiveParser.parse_zip(
            file_path,
            file_name,
            self._get_scan_callback(),
            self.temp_dirs
        )

    def _process_rar_archive(self, file_path, file_name):
        """
        Process a RAR archive.

        Args:
            file_path (str): Path to the RAR archive
            file_name (str): Name of the RAR archive

        Returns:
            bool: True if successful, False otherwise
        """
        self._update_file_progress(config.PROGRESS_ARCHIVE_EXTRACTION, file_name)
        return ArchiveParser.parse_rar(
            file_path,
            file_name,
            self._get_scan_callback(),
            self.temp_dirs
        )

    def _process_7z_archive(self, file_path, file_name):
        """
        Process a 7z archive.

        Args:
            file_path (str): Path to the 7z archive
            file_name (str): Name of the 7z archive

        Returns:
            bool: True if successful, False otherwise
        """
        self._update_file_progress(config.PROGRESS_ARCHIVE_EXTRACTION, file_name)
        return ArchiveParser.parse_7z(
            file_path,
            file_name,
            self._get_scan_callback(),
            self.temp_dirs
        )

    def _update_archive_status(self, file_name, success):
        """
        Update the UI status after processing an archive.

        Args:
            file_name (str): Name of the archive
            success (bool): Whether the archive was processed successfully
        """
        if self.status_callback:
            if success:
                self.status_callback(f"Archive processed: {file_name}")
            else:
                self.status_callback(f"Failed to process archive: {file_name}")

            # After a short delay, update status to show we're returning to regular scan
            time.sleep(config.STATUS_DELAY)
            self.status_callback("Returning to regular scan...")

    def process_archive(self, file_path):
        """
        Process an archive file that was marked during the threaded scan.

        Args:
            file_path (str): Path to the archive file

        Returns:
            bool: True if the archive was processed successfully, False otherwise
        """
        # Get file name and extension
        file_name = os.path.basename(file_path)
        file_ext = get_file_extension(file_path)

        # Initialize archive processing
        self._init_archive_processing(file_name)

        # Process based on archive type
        success = self._process_archive_by_type(file_path, file_name, file_ext)

        # Finalize archive processing
        self._finalize_archive_processing(file_name, success)

        return success

    def _init_archive_processing(self, file_name):
        """
        Initialize archive processing by updating progress and status.

        Args:
            file_name (str): Name of the archive file
        """
        log_search_operation("started", file_name=file_name, item_type="archive")

        # Update current file name for status display
        self.current_file_name = file_name

        # Initialize progress and status
        self._update_file_progress(config.PROGRESS_START, file_name)

        if self.status_callback:
            self.status_callback(f"Processing archive: {file_name}")

        # Update file progress
        self._update_file_progress(config.PROGRESS_FILE_INIT, file_name)

    def _process_archive_by_type(self, file_path, file_name, file_ext):
        """
        Process an archive based on its type.

        Args:
            file_path (str): Path to the archive file
            file_name (str): Name of the archive file
            file_ext (str): Extension of the archive file

        Returns:
            bool: True if the archive was processed successfully, False otherwise
        """
        success = False

        # Determine archive type and process accordingly
        if file_ext == '.zip':
            success = self._process_zip_archive(file_path, file_name)
        elif file_ext == '.rar' and ArchiveParser.is_rar_available():
            success = self._process_rar_archive(file_path, file_name)
        elif (file_ext == '.7z' or file_name.lower().endswith('.7z')) and ArchiveParser.is_7z_available():
            success = self._process_7z_archive(file_path, file_name)

        return success

    def _finalize_archive_processing(self, file_name, success):
        """
        Finalize archive processing by updating progress and status.

        Args:
            file_name (str): Name of the archive file
            success (bool): Whether the archive was processed successfully
        """
        # Process any remaining results before finalizing
        try:
            # Small delay to allow any pending results to be processed
            time.sleep(0.1)

            # Force UI update to ensure all results are displayed
            if self.result_callback and hasattr(self.result_callback, "__self__"):
                try:
                    # Check if the result callback has the _process_result_queue method
                    if hasattr(self.result_callback.__self__, "_process_result_queue"):
                        # Call the method to process any queued results
                        self.result_callback.__self__._process_result_queue()
                except Exception as e:
                    logger.debug(f"Error during archive finalization UI update: {e}")
        except Exception as e:
            logger.debug(f"Error during archive finalization: {e}")

        # Update file progress to complete
        self._update_file_progress(config.PROGRESS_COMPLETE, file_name)

        # Log completion
        status = "successful" if success else "failed"
        log_search_operation("completed", file_name=file_name, item_type="archive", status=status)

        # Update UI status
        self._update_archive_status(file_name, success)

    def add_result(self, result):
        """
        Add a search result.

        Args:
            result (SearchResult): Search result to add
        """
        # Skip archive markers (used for inter-thread communication)
        if result.keyword == config.ARCHIVE_MARKER_KEYWORD:
            # Process the archive in the main thread
            # Note: current_keywords and current_case_sensitive are already set in start_scan
            self.process_archive(result.path)
            return

        # Add to results list (thread-safe)
        self._add_result(result)

        # Dispatch result found event
        event_dispatcher.dispatch(Event(
            EventType.RESULT_FOUND,
            {"result": result}
        ))

        # Call result callback if provided (for backward compatibility)
        if self.result_callback:
            self.result_callback(result)

    def get_results(self):
        """
        Get all search results.

        Returns:
            list: List of SearchResult objects
        """
        return self.results
