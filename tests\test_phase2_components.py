"""
Unit tests for Phase 2 refactored components
Tests the new component-based architecture and code quality improvements
"""

import unittest
import tempfile
import os
import threading
import time
from unittest.mock import Mock, patch, MagicMock

# Import the components to test
from searchtools.ui.components.scan_controller import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ScanStateManager
from searchtools.ui.components.result_manager import ResultManager
from searchtools.ui.components.progress_tracker import ProgressTracker, ThreadSafeProgressTracker
from searchtools.core.result_factory import SearchResultFactory, ResultFormatter
from searchtools.core.result import SearchResult


class TestSearchResultFactory(unittest.TestCase):
    """Test the SearchResult factory for consistent result creation."""
    
    def test_create_text_result(self):
        """Test creating text-based search results."""
        result = SearchResultFactory.create_text_result(
            file_name="test.txt",
            file_path="/path/to/test.txt",
            keyword="test_keyword",
            line_number=42,
            line_content="This is a test_keyword line"
        )
        
        self.assertIsInstance(result, SearchResult)
        self.assertEqual(result.file, "test.txt")
        self.assertEqual(result.path, "/path/to/test.txt")
        self.assertEqual(result.keyword, "test_keyword")
        self.assertEqual(result.location, "Line 42")
        self.assertEqual(result.context, "This is a test_keyword line")
    
    def test_create_csv_result(self):
        """Test creating CSV search results."""
        result = SearchResultFactory.create_csv_result(
            file_name="data.csv",
            file_path="/path/to/data.csv",
            keyword="value",
            column_name="Column1",
            row_number=5,
            cell_content="test value"
        )
        
        self.assertIsInstance(result, SearchResult)
        self.assertEqual(result.file, "data.csv")
        self.assertEqual(result.location, "CSV: Col Column1, Row 5")
        self.assertEqual(result.context, "test value")
    
    def test_create_json_result(self):
        """Test creating JSON search results."""
        result = SearchResultFactory.create_json_result(
            file_name="config.json",
            file_path="/path/to/config.json",
            keyword="setting",
            json_path="data.config.setting",
            value="enabled"
        )
        
        self.assertIsInstance(result, SearchResult)
        self.assertEqual(result.location, "JSON: data.config.setting")
        self.assertEqual(result.context, "enabled")
    
    def test_create_xml_result(self):
        """Test creating XML search results."""
        result = SearchResultFactory.create_xml_result(
            file_name="config.xml",
            file_path="/path/to/config.xml",
            keyword="setting",
            element_path="/root/config/setting",
            element_content="<setting>enabled</setting>"
        )
        
        self.assertIsInstance(result, SearchResult)
        self.assertEqual(result.location, "XML: /root/config/setting")
        self.assertEqual(result.context, "<setting>enabled</setting>")
    
    def test_create_binary_result(self):
        """Test creating binary file search results."""
        result = SearchResultFactory.create_binary_result(
            file_name="binary.exe",
            file_path="/path/to/binary.exe",
            keyword="string",
            offset=1024,
            surrounding_text="...string found..."
        )
        
        self.assertIsInstance(result, SearchResult)
        self.assertEqual(result.location, "Binary: Offset 1024")
        self.assertEqual(result.context, "...string found...")
    
    def test_create_archive_result(self):
        """Test creating archive search results."""
        result = SearchResultFactory.create_archive_result(
            file_name="archive.zip",
            file_path="/path/to/archive.zip",
            keyword="content",
            archive_member="inner.txt",
            member_location="Line 10",
            context="This has content"
        )
        
        self.assertIsInstance(result, SearchResult)
        self.assertEqual(result.location, "Archive: inner.txt -> Line 10")
        self.assertEqual(result.context, "This has content")
    
    def test_create_batch_results(self):
        """Test creating multiple results efficiently."""
        matches = [
            {
                'keyword': 'test1',
                'location_type': 'Text',
                'location_detail': 'Line 1',
                'context': 'First test'
            },
            {
                'keyword': 'test2',
                'location_type': 'Text',
                'location_detail': 'Line 2',
                'context': 'Second test'
            }
        ]
        
        results = SearchResultFactory.create_batch_results(
            file_name="batch.txt",
            file_path="/path/to/batch.txt",
            matches=matches
        )
        
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0].keyword, 'test1')
        self.assertEqual(results[1].keyword, 'test2')


class TestResultFormatter(unittest.TestCase):
    """Test the result formatting utilities."""
    
    def test_format_file_path_relative(self):
        """Test making file paths relative."""
        # Create a temporary file to test with
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            formatted = ResultFormatter.format_file_path(tmp_path, make_relative=True)
            # Should be relative to current directory
            self.assertNotEqual(formatted, tmp_path)
        finally:
            os.unlink(tmp_path)
    
    def test_truncate_context(self):
        """Test context truncation."""
        long_context = "This is a very long context string that should be truncated"
        truncated = ResultFormatter.truncate_context(long_context, max_length=20)
        
        self.assertEqual(len(truncated), 20)
        self.assertTrue(truncated.endswith("..."))
    
    def test_clean_context(self):
        """Test context cleaning."""
        dirty_context = "  This\thas\n\nextra   whitespace  \r\n"
        cleaned = ResultFormatter.clean_context(dirty_context)
        
        self.assertEqual(cleaned, "This has extra whitespace")
    
    def test_highlight_keyword(self):
        """Test keyword highlighting."""
        context = "This is a test string with test keyword"
        highlighted = ResultFormatter.highlight_keyword(context, "test")
        
        self.assertIn("**test**", highlighted)
        # Should highlight both occurrences
        self.assertEqual(highlighted.count("**test**"), 2)


class TestScanController(unittest.TestCase):
    """Test the scan controller functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.scan_controller = ScanController()
        self.mock_callbacks = {
            'progress': Mock(),
            'status': Mock(),
            'result': Mock(),
            'file_progress': Mock(),
            'scan_state': Mock()
        }
        
        self.scan_controller.set_callbacks(
            progress_callback=self.mock_callbacks['progress'],
            status_callback=self.mock_callbacks['status'],
            result_callback=self.mock_callbacks['result'],
            file_progress_callback=self.mock_callbacks['file_progress'],
            scan_state_callback=self.mock_callbacks['scan_state']
        )
    
    def test_initialization(self):
        """Test scan controller initialization."""
        self.assertIsNotNone(self.scan_controller.scanner)
        self.assertFalse(self.scan_controller.scan_in_progress)
    
    def test_validate_scan_inputs_empty_folder(self):
        """Test input validation with empty folder."""
        result = self.scan_controller._validate_scan_inputs("", "test")
        self.assertFalse(result)
    
    @patch('searchtools.ui.components.scan_controller.messagebox.showerror')
    def test_validate_scan_inputs_empty_keywords(self, mock_showerror):
        """Test input validation with empty keywords."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            result = self.scan_controller._validate_scan_inputs(tmp_dir, "")
            self.assertFalse(result)
            mock_showerror.assert_called_once()
    
    def test_parse_keywords(self):
        """Test keyword parsing."""
        keywords = self.scan_controller._parse_keywords("test1, test2, test3")
        self.assertEqual(keywords, ["test1", "test2", "test3"])
        
        # Test with extra whitespace
        keywords = self.scan_controller._parse_keywords("  test1  ,  test2  ")
        self.assertEqual(keywords, ["test1", "test2"])
    
    @patch('os.path.isdir')
    def test_start_scan_success(self, mock_isdir):
        """Test successful scan start."""
        mock_isdir.return_value = True
        
        with patch.object(self.scan_controller.scanner, 'start_scan') as mock_start:
            result = self.scan_controller.start_scan(
                folder="/test/path",
                keywords_text="test1, test2",
                case_sensitive=True
            )
            
            self.assertTrue(result)
            self.assertTrue(self.scan_controller.scan_in_progress)
            mock_start.assert_called_once()
    
    def test_stop_scan(self):
        """Test scan stopping."""
        self.scan_controller.scan_in_progress = True
        
        with patch.object(self.scan_controller.scanner, 'stop_scanning') as mock_stop:
            self.scan_controller.stop_scan()
            mock_stop.assert_called_once()


class TestProgressTracker(unittest.TestCase):
    """Test the progress tracking functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_status_manager = Mock()
        self.progress_tracker = ProgressTracker(self.mock_status_manager)
    
    def test_initialization(self):
        """Test progress tracker initialization."""
        self.assertEqual(self.progress_tracker.overall_progress, 0)
        self.assertEqual(self.progress_tracker.file_progress, 0)
        self.assertEqual(self.progress_tracker.archives_progress, 0)
        self.assertEqual(self.progress_tracker.current_status, "Ready")
    
    def test_update_overall_progress(self):
        """Test overall progress updates."""
        self.progress_tracker.update_overall_progress(50)
        
        self.assertEqual(self.progress_tracker.overall_progress, 50)
        self.mock_status_manager.update_overall_progress.assert_called_with(50)
    
    def test_update_file_progress(self):
        """Test file progress updates."""
        self.progress_tracker.update_file_progress(75, "test.txt")
        
        self.assertEqual(self.progress_tracker.file_progress, 75)
        self.mock_status_manager.update_file_progress.assert_called_with(75)
    
    def test_update_status(self):
        """Test status updates."""
        self.progress_tracker.update_status("Processing files...")
        
        self.assertEqual(self.progress_tracker.current_status, "Processing files...")
        self.mock_status_manager.update_status.assert_called_with("Processing files...")
    
    def test_reset_progress(self):
        """Test progress reset."""
        # Set some progress first
        self.progress_tracker.update_overall_progress(50)
        self.progress_tracker.update_file_progress(75)
        
        # Reset
        self.progress_tracker.reset_progress()
        
        self.assertEqual(self.progress_tracker.overall_progress, 0)
        self.assertEqual(self.progress_tracker.file_progress, 0)
        self.assertEqual(self.progress_tracker.current_status, "Ready")
    
    def test_get_progress_state(self):
        """Test getting progress state."""
        self.progress_tracker.update_overall_progress(25)
        self.progress_tracker.update_file_progress(50)
        self.progress_tracker.update_status("Testing")
        
        state = self.progress_tracker.get_progress_state()
        
        expected = {
            'overall_progress': 25,
            'file_progress': 50,
            'archives_progress': 0,
            'status': 'Testing'
        }
        self.assertEqual(state, expected)


class TestThreadSafeProgressTracker(unittest.TestCase):
    """Test thread-safe progress tracking."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_progress_tracker = Mock()
        self.thread_safe_tracker = ThreadSafeProgressTracker(self.mock_progress_tracker)
    
    def test_thread_safe_updates(self):
        """Test that updates are thread-safe."""
        # Simulate multiple threads updating progress
        def update_progress(progress_type, value):
            self.thread_safe_tracker.update_progress_threadsafe(progress_type, value)
        
        threads = []
        for i in range(10):
            thread = threading.Thread(target=update_progress, args=('overall', i * 10))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Force immediate processing
        self.thread_safe_tracker.force_update_now()
        
        # Should have called the underlying tracker
        self.mock_progress_tracker.update_overall_progress.assert_called()


class TestResultManager(unittest.TestCase):
    """Test the result management functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_results_frame = Mock()
        self.mock_root = Mock()
        self.result_manager = ResultManager(self.mock_results_frame, self.mock_root)
    
    def test_initialization(self):
        """Test result manager initialization."""
        self.assertEqual(len(self.result_manager.results), 0)
        self.assertTrue(self.result_manager.result_queue.empty())
    
    def test_add_result(self):
        """Test adding a result."""
        result = SearchResult(
            file="test.txt",
            path="/path/to/test.txt",
            keyword="test",
            context="test context",
            location="Line 1",
            full_context="test context"
        )
        
        self.result_manager.add_result(result)
        
        # Result should be queued
        self.assertFalse(self.result_manager.result_queue.empty())
    
    def test_clear_results(self):
        """Test clearing results."""
        # Add a result first
        result = SearchResult(
            file="test.txt",
            path="/path/to/test.txt",
            keyword="test",
            context="test context",
            location="Line 1",
            full_context="test context"
        )
        self.result_manager.results.append(result)
        
        # Clear results
        self.result_manager.clear_results()
        
        self.assertEqual(len(self.result_manager.results), 0)
        self.mock_results_frame.clear_results.assert_called_once()
    
    def test_get_results_count(self):
        """Test getting results count."""
        # Add some results
        for i in range(3):
            result = SearchResult(
                file=f"test{i}.txt",
                path=f"/path/to/test{i}.txt",
                keyword="test",
                context="test context",
                location=f"Line {i+1}",
                full_context="test context"
            )
            self.result_manager.results.append(result)
        
        self.assertEqual(self.result_manager.get_results_count(), 3)


if __name__ == '__main__':
    unittest.main()
