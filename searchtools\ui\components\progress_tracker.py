"""
Progress Tracker for SearchTools application
Handles progress tracking and event management
"""

import threading
from searchtools.utils.logging import logger
from searchtools.utils.events import EventType, event_dispatcher


class ProgressTracker:
    """
    Tracks and manages progress updates for scan operations.
    """
    
    def __init__(self, status_frame_manager):
        """
        Initialize the progress tracker.
        
        Args:
            status_frame_manager: StatusFrameManager instance for UI updates
        """
        self.status_frame_manager = status_frame_manager
        self._lock = threading.RLock()
        
        # Progress state
        self.overall_progress = 0
        self.file_progress = 0
        self.archives_progress = 0
        self.current_status = "Ready"
        
        # Subscribe to events
        self._subscribe_to_events()
    
    def _subscribe_to_events(self):
        """Subscribe to scanner events."""
        # Subscribe to progress update events
        event_dispatcher.subscribe(
            EventType.PROGRESS_UPDATE,
            self._handle_progress_event
        )
        
        # Subscribe to status update events
        event_dispatcher.subscribe(
            EventType.STATUS_UPDATE,
            self._handle_status_event
        )
        
        # Subscribe to file progress update events
        event_dispatcher.subscribe(
            EventType.FILE_PROGRESS_UPDATE,
            self._handle_file_progress_event
        )
    
    def unsubscribe_from_events(self):
        """Unsubscribe from all events."""
        event_dispatcher.unsubscribe(EventType.PROGRESS_UPDATE, self._handle_progress_event)
        event_dispatcher.unsubscribe(EventType.STATUS_UPDATE, self._handle_status_event)
        event_dispatcher.unsubscribe(EventType.FILE_PROGRESS_UPDATE, self._handle_file_progress_event)
    
    def update_overall_progress(self, progress):
        """
        Update the overall progress with throttling to prevent excessive UI updates.

        Args:
            progress (float): Progress value (0-100)
        """
        with self._lock:
            # Only update if progress changed significantly (reduces UI updates)
            if abs(progress - self.overall_progress) >= 1.0 or progress >= 100:
                self.overall_progress = progress
                self.status_frame_manager.update_overall_progress(progress)
                logger.debug(f"Overall progress updated: {progress}%")
    
    def update_file_progress(self, progress, file_name=None):
        """
        Update the file progress with throttling for better performance.

        Args:
            progress (float): Progress value (0-100)
            file_name (str, optional): Name of the current file being processed
        """
        with self._lock:
            # Only update if progress changed significantly or completed
            if abs(progress - self.file_progress) >= 2.0 or progress >= 100 or progress == 0:
                self.file_progress = progress
                self.status_frame_manager.update_file_progress(progress)

                if file_name:
                    logger.debug(f"File progress updated: {progress}% for {file_name}")
                else:
                    logger.debug(f"File progress updated: {progress}%")
    
    def update_archives_progress(self, progress):
        """
        Update the archives progress with throttling.

        Args:
            progress (float): Progress value (0-100)
        """
        with self._lock:
            # Only update if progress changed significantly
            if abs(progress - self.archives_progress) >= 1.0 or progress >= 100:
                self.archives_progress = progress
                self.status_frame_manager.update_archives_progress(progress)
                logger.debug(f"Archives progress updated: {progress}%")
    
    def update_status(self, status):
        """
        Update the status message.
        
        Args:
            status (str): Status message to display
        """
        with self._lock:
            self.current_status = status
            self.status_frame_manager.update_status(status)
            logger.debug(f"Status updated: {status}")
    
    def reset_progress(self):
        """Reset all progress indicators."""
        with self._lock:
            self.overall_progress = 0
            self.file_progress = 0
            self.archives_progress = 0
            self.current_status = "Ready"
            
            self.status_frame_manager.reset_progress_bars()
            self.status_frame_manager.update_status("Ready")
            
            logger.debug("All progress indicators reset")
    
    def get_progress_state(self):
        """
        Get the current progress state.
        
        Returns:
            dict: Dictionary containing current progress values
        """
        with self._lock:
            return {
                'overall_progress': self.overall_progress,
                'file_progress': self.file_progress,
                'archives_progress': self.archives_progress,
                'status': self.current_status
            }
    
    # Event handlers
    def _handle_progress_event(self, event):
        """
        Handle progress update events from the scanner.
        
        Args:
            event: Event object containing progress data
        """
        try:
            progress_data = event.data
            progress = progress_data.get('progress', 0)
            self.update_overall_progress(progress)
        except Exception as e:
            logger.error(f"Error handling progress event: {e}")
    
    def _handle_status_event(self, event):
        """
        Handle status update events from the scanner.
        
        Args:
            event: Event object containing status data
        """
        try:
            status_data = event.data
            status = status_data.get('status', 'Unknown')
            self.update_status(status)
        except Exception as e:
            logger.error(f"Error handling status event: {e}")
    
    def _handle_file_progress_event(self, event):
        """
        Handle file progress update events from the scanner.
        
        Args:
            event: Event object containing file progress data
        """
        try:
            progress_data = event.data
            progress = progress_data.get('progress', 0)
            file_name = progress_data.get('file_name', None)
            self.update_file_progress(progress, file_name)
        except Exception as e:
            logger.error(f"Error handling file progress event: {e}")


class ThreadSafeProgressTracker:
    """
    Thread-safe wrapper for progress tracking operations.
    Ensures that progress updates from multiple threads are handled safely.
    """
    
    def __init__(self, progress_tracker):
        """
        Initialize the thread-safe progress tracker.
        
        Args:
            progress_tracker: ProgressTracker instance to wrap
        """
        self.progress_tracker = progress_tracker
        self._update_lock = threading.RLock()
        self._batch_updates = {}
        self._batch_timer = None
    
    def update_progress_threadsafe(self, progress_type, value, **kwargs):
        """
        Update progress in a thread-safe manner with batching.
        
        Args:
            progress_type (str): Type of progress ('overall', 'file', 'archives', 'status')
            value: Progress value or status message
            **kwargs: Additional arguments for the update
        """
        with self._update_lock:
            # Store the update in the batch
            self._batch_updates[progress_type] = {
                'value': value,
                'kwargs': kwargs
            }
            
            # Schedule batch processing if not already scheduled
            if self._batch_timer is None:
                import threading
                self._batch_timer = threading.Timer(0.1, self._process_batch_updates)
                self._batch_timer.start()
    
    def _process_batch_updates(self):
        """Process batched progress updates."""
        with self._update_lock:
            # Process all batched updates
            for progress_type, update_data in self._batch_updates.items():
                value = update_data['value']
                kwargs = update_data['kwargs']
                
                try:
                    if progress_type == 'overall':
                        self.progress_tracker.update_overall_progress(value)
                    elif progress_type == 'file':
                        file_name = kwargs.get('file_name')
                        self.progress_tracker.update_file_progress(value, file_name)
                    elif progress_type == 'archives':
                        self.progress_tracker.update_archives_progress(value)
                    elif progress_type == 'status':
                        self.progress_tracker.update_status(value)
                except Exception as e:
                    logger.error(f"Error processing {progress_type} update: {e}")
            
            # Clear the batch and timer
            self._batch_updates.clear()
            self._batch_timer = None
    
    def force_update_now(self):
        """Force immediate processing of any pending updates."""
        if self._batch_timer:
            self._batch_timer.cancel()
            self._process_batch_updates()


class ProgressCalculator:
    """
    Utility class for calculating progress values based on different metrics.
    """
    
    @staticmethod
    def calculate_file_progress(current_file, total_files):
        """
        Calculate progress based on file count.
        
        Args:
            current_file (int): Current file number (0-based)
            total_files (int): Total number of files
            
        Returns:
            float: Progress percentage (0-100)
        """
        if total_files <= 0:
            return 0
        
        progress = (current_file / total_files) * 100
        return min(100, max(0, progress))
    
    @staticmethod
    def calculate_size_progress(current_size, total_size):
        """
        Calculate progress based on data size.
        
        Args:
            current_size (int): Current processed size in bytes
            total_size (int): Total size in bytes
            
        Returns:
            float: Progress percentage (0-100)
        """
        if total_size <= 0:
            return 0
        
        progress = (current_size / total_size) * 100
        return min(100, max(0, progress))
    
    @staticmethod
    def calculate_weighted_progress(progresses, weights=None):
        """
        Calculate weighted average progress from multiple progress values.
        
        Args:
            progresses (list): List of progress values (0-100)
            weights (list, optional): List of weights for each progress value
            
        Returns:
            float: Weighted average progress (0-100)
        """
        if not progresses:
            return 0
        
        if weights is None:
            weights = [1] * len(progresses)
        
        if len(progresses) != len(weights):
            raise ValueError("Number of progresses must match number of weights")
        
        total_weight = sum(weights)
        if total_weight <= 0:
            return 0
        
        weighted_sum = sum(p * w for p, w in zip(progresses, weights))
        return weighted_sum / total_weight
    
    @staticmethod
    def estimate_remaining_time(current_progress, elapsed_time):
        """
        Estimate remaining time based on current progress and elapsed time.
        
        Args:
            current_progress (float): Current progress (0-100)
            elapsed_time (float): Elapsed time in seconds
            
        Returns:
            float: Estimated remaining time in seconds, or None if cannot estimate
        """
        if current_progress <= 0 or elapsed_time <= 0:
            return None
        
        # Calculate rate of progress
        progress_rate = current_progress / elapsed_time  # percent per second
        
        if progress_rate <= 0:
            return None
        
        remaining_progress = 100 - current_progress
        estimated_remaining = remaining_progress / progress_rate
        
        return max(0, estimated_remaining)
    
    @staticmethod
    def format_time_estimate(seconds):
        """
        Format time estimate in a human-readable format.
        
        Args:
            seconds (float): Time in seconds
            
        Returns:
            str: Formatted time string
        """
        if seconds is None or seconds < 0:
            return "Unknown"
        
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes}m {secs}s"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h {minutes}m"
