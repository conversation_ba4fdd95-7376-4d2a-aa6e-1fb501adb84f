"""
Refactored MainWindow class for SearchTools application
Uses component-based architecture with separated concerns
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox

from searchtools.ui.components.ui_manager import (
    UIStyleManager, Input<PERSON><PERSON>eManager, Results<PERSON><PERSON>eManager, StatusFrameManager
)
from searchtools.ui.components.scan_controller import <PERSON><PERSON><PERSON>ontroll<PERSON>, ScanStateManager
from searchtools.ui.components.result_manager import ResultManager, ResultDetailsManager
from searchtools.ui.components.progress_tracker import ProgressTracker
from searchtools.utils.logging import logger
from searchtools.utils.error_handling import handle_ui_operation
import config


class MainWindow:
    """
    Main application window with refactored component-based architecture.
    
    This class now delegates responsibilities to specialized components:
    - UIStyleManager: Handles UI styling and theming
    - InputFrameManager: Manages input controls and settings
    - ResultsFrameManager: Manages results display
    - StatusFrameManager: Manages progress bars and status
    - ScanController: Controls scan operations
    - ResultManager: Manages result storage and export
    - ProgressTracker: Tracks and updates progress
    """
    
    def __init__(self, root):
        """
        Initialize the main window with component-based architecture.
        
        Args:
            root (tk.Tk): Root window
        """
        self.root = root
        self.setup_window()
        
        # Initialize UI components
        self._initialize_ui_components()
        
        # Initialize business logic components
        self._initialize_business_components()
        
        # Set up component interactions
        self._setup_component_interactions()
        
        # Create the UI
        self._create_ui()
        
        # Apply styling
        self.ui_style_manager.apply_styling()
        
        logger.info("MainWindow initialized with component-based architecture")
    
    def setup_window(self):
        """Set up the main window properties."""
        self.root.title(config.APP_TITLE)
        self.root.geometry(config.WINDOW_SIZE)
        self.root.minsize(config.MIN_WINDOW_WIDTH, config.MIN_WINDOW_HEIGHT)
        
        # Set window icon if available
        self._set_window_icon()
        
        # Handle window close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def _set_window_icon(self):
        """Set the window icon if available."""
        try:
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception as e:
            logger.debug(f"Could not set window icon: {e}")
    
    def _initialize_ui_components(self):
        """Initialize UI management components."""
        # Create main frames
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Initialize UI managers
        self.ui_style_manager = UIStyleManager(self.root)
        self.input_frame_manager = InputFrameManager(self.main_frame)
        self.results_frame_manager = ResultsFrameManager(self.main_frame)
        self.status_frame_manager = StatusFrameManager(self.main_frame)
    
    def _initialize_business_components(self):
        """Initialize business logic components."""
        # Initialize controllers and managers
        self.scan_controller = ScanController()
        self.result_manager = ResultManager(self.results_frame_manager, self.root)
        self.result_details_manager = ResultDetailsManager(self.root)
        self.progress_tracker = ProgressTracker(self.status_frame_manager)
        
        # Initialize state manager
        self.scan_state_manager = ScanStateManager(
            self.input_frame_manager,
            self.status_frame_manager
        )
    
    def _setup_component_interactions(self):
        """Set up interactions between components."""
        # Set up scan controller callbacks
        self.scan_controller.set_callbacks(
            progress_callback=self.progress_tracker.update_overall_progress,
            status_callback=self.progress_tracker.update_status,
            result_callback=self.result_manager.add_result,
            file_progress_callback=self.progress_tracker.update_file_progress,
            scan_state_callback=self._on_scan_state_changed
        )
        
        # Set up result manager callbacks
        self.result_manager.set_state_update_callback(
            self.scan_state_manager.update_button_states_for_results
        )
    
    def _create_ui(self):
        """Create the user interface."""
        # Create input frame with callbacks
        self.input_frame_manager.create_input_frame(
            start_callback=self.start_scan,
            stop_callback=self.stop_scan,
            export_callback=self.export_results,
            clear_callback=self.clear_results,
            browse_callback=self.browse_folder
        )
        
        # Create results frame
        self.results_frame_manager.create_results_frame(
            double_click_callback=self.on_result_double_click
        )
        
        # Create status frame
        self.status_frame_manager.create_status_frame()
    
    # Event handlers
    @handle_ui_operation(error_message="Error starting scan")
    def start_scan(self):
        """Start a scan operation."""
        # Get input values
        input_values = self.input_frame_manager.get_input_values()
        
        # Update scan state
        self.scan_state_manager.on_scan_started()
        
        # Start the scan
        success = self.scan_controller.start_scan(
            folder=input_values['folder'],
            keywords_text=input_values['keywords'],
            case_sensitive=input_values['case_sensitive'],
            use_regex=input_values['regex'],
            whole_word=input_values['whole_word']
        )
        
        if not success:
            # Scan failed to start, revert state
            self.scan_state_manager.on_scan_stopped()
    
    @handle_ui_operation(error_message="Error stopping scan")
    def stop_scan(self):
        """Stop the current scan operation."""
        self.scan_state_manager.on_scan_stopping()
        self.scan_controller.stop_scan()
    
    @handle_ui_operation(error_message="Error exporting results")
    def export_results(self):
        """Export the search results."""
        self.result_manager.export_results()
    
    @handle_ui_operation(error_message="Error clearing results")
    def clear_results(self):
        """Clear all search results."""
        self.result_manager.clear_results()
        self.progress_tracker.reset_progress()
    
    @handle_ui_operation(error_message="Error browsing for folder")
    def browse_folder(self):
        """Browse for a folder or file to scan."""
        # Import ArchiveParser to check supported formats
        from searchtools.parsers.archive_parser import ArchiveParser
        
        # Create file type filters
        filetypes = [
            ("All supported", "*.zip;*.rar;*.7z"),
            ("ZIP files", "*.zip"),
            ("RAR files", "*.rar"),
            ("7Z files", "*.7z"),
            ("All files", "*.*")
        ]
        
        # Ask user to choose between folder and file
        choice = messagebox.askyesnocancel(
            "Select Input",
            "Do you want to select a folder to scan?\n\n"
            "Yes: Select a folder\n"
            "No: Select an archive file (ZIP, RAR, 7Z)\n"
            "Cancel: Cancel selection"
        )
        
        if choice is None:  # Cancel
            return
        elif choice:  # Yes - select folder
            folder_path = filedialog.askdirectory(title="Select Folder to Scan")
            if folder_path:
                self.input_frame_manager.folder_var.set(folder_path)
        else:  # No - select file
            file_path = filedialog.askopenfilename(
                title="Select Archive File to Scan",
                filetypes=filetypes
            )
            if file_path:
                self.input_frame_manager.folder_var.set(file_path)
    
    @handle_ui_operation(error_message="Error opening result details")
    def on_result_double_click(self, event):
        """Handle double-click on a result item."""
        selected_result = self.result_manager.get_selected_result()
        if selected_result:
            self.result_details_manager.show_result_details(selected_result)
    
    def _on_scan_state_changed(self, scan_started):
        """
        Handle scan state changes.
        
        Args:
            scan_started (bool): True if scan started, False if stopped
        """
        if scan_started:
            self.scan_state_manager.on_scan_started()
        else:
            self.scan_state_manager.on_scan_stopped()
    
    @handle_ui_operation(error_message="Error during window closing")
    def on_closing(self):
        """Handle window close event."""
        # Stop any running scan
        if self.scan_controller.is_scan_in_progress():
            logger.info("Stopping scan before closing application")
            self.scan_controller.stop_scan()
            
            # Give it a moment to stop gracefully
            self.root.after(1000, self._force_close)
        else:
            self._cleanup_and_close()
    
    def _force_close(self):
        """Force close the application after timeout."""
        logger.info("Force closing application")
        self._cleanup_and_close()
    
    def _cleanup_and_close(self):
        """Clean up resources and close the application."""
        try:
            # Clean up temporary directories
            self.scan_controller.cleanup_temp_dirs()
            
            # Unsubscribe from events
            self.progress_tracker.unsubscribe_from_events()
            
            logger.info("Application closing - cleanup complete")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
        finally:
            self.root.destroy()
    
    # Public API for external access
    def get_results_count(self):
        """
        Get the number of search results.
        
        Returns:
            int: Number of results
        """
        return self.result_manager.get_results_count()
    
    def get_scan_controller(self):
        """
        Get the scan controller for external access.
        
        Returns:
            ScanController: The scan controller instance
        """
        return self.scan_controller
    
    def get_result_manager(self):
        """
        Get the result manager for external access.
        
        Returns:
            ResultManager: The result manager instance
        """
        return self.result_manager
