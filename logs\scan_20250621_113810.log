2025-06-21 11:38:10,351 - DEBUG - Started periodic cleanup thread
2025-06-21 11:38:12,494 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-06-21 11:38:12,652 - INFO - win32com configured. DOC parsing support enabled.
2025-06-21 11:38:12,654 - DEBUG - antiword not available as fallback for DOC files.
2025-06-21 11:38:12,897 - DEBUG - Validated extraction: normal.txt (1024 bytes)
2025-06-21 11:38:12,912 - DEBUG - Created secure temporary directory: C:\Users\<USER>\AppData\Local\Temp\searchtools_secure_5g4gvpfm
2025-06-21 11:38:12,915 - DEBUG - Created temporary directory: C:\Users\<USER>\AppData\Local\Temp\searchtools_secure_5g4gvpfm
2025-06-21 11:38:12,927 - WARNING - Failed to clean up temporary directory: C:\Users\<USER>\AppData\Local\Temp\searchtools_secure_5g4gvpfm
2025-06-21 11:38:13,273 - DEBUG - Cleaned up temporary directory: C:\Users\<USER>\AppData\Local\Temp\searchtools_secure_5g4gvpfm
2025-06-21 11:38:13,274 - INFO - Cleaned up 1 temporary directories
