"""
UI Management classes for SearchTools application
Handles UI creation, styling, and layout management
"""

import os
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk

from searchtools.utils.logging import logger
from searchtools.utils.error_handling import handle_ui_operation
import config


class UIStyleManager:
    """
    Manages UI styling and theming for the application.
    """
    
    def __init__(self, root):
        """
        Initialize the style manager.
        
        Args:
            root (tk.Tk): Root window
        """
        self.root = root
        self.style = ttk.Style()
    
    @handle_ui_operation(error_message="Error applying UI styling")
    def apply_styling(self):
        """
        Apply custom styling to the UI.
        """
        # Apply styles to different widget types
        self._style_common_elements()
        self._style_treeview()
        self._style_progressbar()
        self._style_labelframes()
        
        # Set root window styling
        self._style_root_window()
        
        logger.info("Custom styling applied")
    
    def _style_common_elements(self):
        """
        Style common UI elements like frames, labels, buttons, etc.
        """
        # Configure button style
        self.style.configure(
            "TButton",
            padding=(10, 5),
            font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_NORMAL)
        )

        # Configure label style - default to black text as per user preference
        self.style.configure(
            "TLabel",
            font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_NORMAL),
            foreground="black"
        )

        # Configure entry style
        self.style.configure(
            "TEntry",
            fieldbackground="white",
            borderwidth=1,
            relief="solid"
        )

        # Configure special styles for sections that should have white text
        # (File Information, Match Information, and Full Context sections)
        self.style.configure(
            "WhiteText.TLabel",
            font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_NORMAL),
            foreground="white",
            background=config.UI_COLORS["secondary_color"]
        )
    
    def _style_treeview(self):
        """
        Style the treeview widget.
        """
        self.style.configure(
            "Treeview",
            background="white",
            foreground="black",
            rowheight=25,
            fieldbackground="white"
        )
        
        self.style.configure(
            "Treeview.Heading",
            background=config.UI_COLORS["secondary_color"],
            foreground="white",
            font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_NORMAL, "bold")
        )
    
    def _style_progressbar(self):
        """
        Style the progressbar widget and create a warning style variant.
        """
        # Default progress bar style
        self.style.configure(
            "TProgressbar",
            background=config.UI_COLORS["primary_color"],
            troughcolor=config.UI_COLORS["bg_color"],
            borderwidth=1,
            lightcolor=config.UI_COLORS["primary_color"],
            darkcolor=config.UI_COLORS["primary_color"]
        )
        
        # Warning progress bar style (for when stopping)
        self.style.configure(
            "Warning.TProgressbar",
            background="orange",
            troughcolor=config.UI_COLORS["bg_color"],
            borderwidth=1,
            lightcolor="orange",
            darkcolor="orange"
        )
    
    def _style_labelframes(self):
        """
        Style the labelframe widgets.
        """
        # Default labelframe style
        self.style.configure(
            "TLabelframe",
            background=config.UI_COLORS["bg_color"],
            foreground="black"
        )
        
        # White text labelframe style
        self.style.configure(
            "WhiteText.TLabelframe",
            background=config.UI_COLORS["secondary_color"],
            foreground="white"
        )
        
        self.style.configure(
            "WhiteText.TLabelframe.Label",
            background=config.UI_COLORS["secondary_color"],
            foreground="white",
            font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_NORMAL, "bold")
        )
    
    def _style_root_window(self):
        """
        Style the root window and set default widget colors.
        """
        # Set background color for the root window
        self.root.configure(background=config.UI_COLORS["bg_color"])
        
        # Set default text color for all widgets
        self.root.option_add("*foreground", "black")
        self.root.option_add("*background", "white")
        self.root.option_add("*Entry.background", "white")
        self.root.option_add("*Entry.foreground", "black")
        self.root.option_add("*Text.background", "white")
        self.root.option_add("*Text.foreground", "black")


class InputFrameManager:
    """
    Manages the input frame with folder selection and keyword input.
    """
    
    def __init__(self, parent_frame):
        """
        Initialize the input frame manager.
        
        Args:
            parent_frame: Parent frame to contain the input frame
        """
        self.parent_frame = parent_frame
        self.input_frame = None
        
        # UI variables
        self.folder_var = tk.StringVar()
        self.keywords_var = tk.StringVar()
        self.case_sensitive_var = tk.BooleanVar()
        self.regex_var = tk.BooleanVar()
        self.whole_word_var = tk.BooleanVar()
        
        # UI components
        self.start_button = None
        self.stop_button = None
        self.export_button = None
        self.clear_button = None
    
    def create_input_frame(self, start_callback, stop_callback, export_callback, 
                          clear_callback, browse_callback):
        """
        Create the input frame with all controls.
        
        Args:
            start_callback: Callback for start scan button
            stop_callback: Callback for stop scan button
            export_callback: Callback for export button
            clear_callback: Callback for clear button
            browse_callback: Callback for browse button
        """
        # Create the input frame
        self.input_frame = ttk.LabelFrame(
            self.parent_frame, 
            text="Search Settings", 
            style="WhiteText.TLabelframe"
        )
        self.input_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Create folder selection
        self._create_folder_selection(browse_callback)
        
        # Create keywords input
        self._create_keywords_input()
        
        # Create buttons and options
        self._create_buttons_and_options(
            start_callback, stop_callback, export_callback, clear_callback
        )
    
    def _create_folder_selection(self, browse_callback):
        """Create the folder selection controls."""
        folder_frame = ttk.Frame(self.input_frame)
        folder_frame.pack(fill=tk.X, padx=5, pady=5)
        
        folder_label = ttk.Label(
            folder_frame,
            text="Folder/File:",
            foreground="black"
        )
        folder_label.pack(side=tk.LEFT, padx=5)
        
        self.folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_var)
        self.folder_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        browse_button = ttk.Button(folder_frame, text="Browse", command=browse_callback)
        browse_button.pack(side=tk.RIGHT, padx=5)
    
    def _create_keywords_input(self):
        """Create the keywords input controls."""
        keywords_frame = ttk.Frame(self.input_frame)
        keywords_frame.pack(fill=tk.X, padx=5, pady=5)
        
        keywords_label = ttk.Label(
            keywords_frame,
            text="Keywords:",
            foreground="black"
        )
        keywords_label.pack(side=tk.LEFT, padx=5)
        
        self.keywords_entry = ttk.Entry(keywords_frame, textvariable=self.keywords_var)
        self.keywords_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
    
    def _create_buttons_and_options(self, start_callback, stop_callback, 
                                   export_callback, clear_callback):
        """Create buttons and option checkboxes."""
        buttons_frame = ttk.Frame(self.input_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Left side: Buttons
        self.start_button = ttk.Button(
            buttons_frame, text="Start Scan", command=start_callback
        )
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(
            buttons_frame, text="Stop Scan", command=stop_callback, state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.export_button = ttk.Button(
            buttons_frame, text="Export Results", command=export_callback, state=tk.DISABLED
        )
        self.export_button.pack(side=tk.LEFT, padx=5)
        
        self.clear_button = ttk.Button(
            buttons_frame, text="Clear Results", command=clear_callback, state=tk.DISABLED
        )
        self.clear_button.pack(side=tk.LEFT, padx=5)
        
        # Center: Checkboxes (green area)
        checkboxes_frame = ttk.Frame(buttons_frame)
        checkboxes_frame.pack(side=tk.LEFT, padx=20)
        
        self.case_sensitive_cb = ttk.Checkbutton(
            checkboxes_frame, text="Case Sensitive", variable=self.case_sensitive_var
        )
        self.case_sensitive_cb.pack(side=tk.LEFT, padx=5)
        
        self.regex_cb = ttk.Checkbutton(
            checkboxes_frame, text="Regex", variable=self.regex_var
        )
        self.regex_cb.pack(side=tk.LEFT, padx=5)
        
        self.whole_word_cb = ttk.Checkbutton(
            checkboxes_frame, text="Whole Word", variable=self.whole_word_var
        )
        self.whole_word_cb.pack(side=tk.LEFT, padx=5)
        
        # Right side: Logo
        self._create_logo_section(buttons_frame)
    
    def _create_logo_section(self, parent_frame):
        """Create the logo section on the right side."""
        logo_frame = ttk.Frame(parent_frame)
        logo_frame.pack(side=tk.RIGHT, padx=10)
        
        try:
            self._create_image_logo(logo_frame)
        except Exception as e:
            logger.debug(f"Could not load image logo: {e}")
            self._create_text_logo(logo_frame)
    
    def _create_image_logo(self, parent_frame):
        """Create an image-based logo."""
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "..", "icon.ico.webp")
        
        if os.path.exists(icon_path):
            image = Image.open(icon_path)
            image = image.resize((32, 32), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(image)
            
            logo_label = ttk.Label(parent_frame, image=photo)
            logo_label.image = photo  # Keep a reference
            logo_label.pack(side=tk.RIGHT)
        else:
            raise FileNotFoundError("Logo file not found")
    
    def _create_text_logo(self, parent_frame):
        """Create a text-based logo as a fallback."""
        logo_label = ttk.Label(
            parent_frame, 
            text="🔍", 
            font=(config.UI_FONT_FAMILY, 20)
        )
        logo_label.pack(side=tk.RIGHT)
    
    def get_input_values(self):
        """
        Get current input values.
        
        Returns:
            dict: Dictionary containing all input values
        """
        return {
            'folder': self.folder_var.get(),
            'keywords': self.keywords_var.get(),
            'case_sensitive': self.case_sensitive_var.get(),
            'regex': self.regex_var.get(),
            'whole_word': self.whole_word_var.get()
        }
    
    def set_button_states(self, start_enabled=True, stop_enabled=False, 
                         export_enabled=False, clear_enabled=False):
        """
        Set the state of buttons.
        
        Args:
            start_enabled (bool): Enable/disable start button
            stop_enabled (bool): Enable/disable stop button
            export_enabled (bool): Enable/disable export button
            clear_enabled (bool): Enable/disable clear button
        """
        self.start_button["state"] = tk.NORMAL if start_enabled else tk.DISABLED
        self.stop_button["state"] = tk.NORMAL if stop_enabled else tk.DISABLED
        self.export_button["state"] = tk.NORMAL if export_enabled else tk.DISABLED
        self.clear_button["state"] = tk.NORMAL if clear_enabled else tk.DISABLED


class ResultsFrameManager:
    """
    Manages the results frame with treeview for displaying search results.
    """

    def __init__(self, parent_frame):
        """
        Initialize the results frame manager.

        Args:
            parent_frame: Parent frame to contain the results frame
        """
        self.parent_frame = parent_frame
        self.results_frame = None
        self.tree = None

    def create_results_frame(self, double_click_callback):
        """
        Create the results frame with treeview.

        Args:
            double_click_callback: Callback for double-click on result items
        """
        # Create the results frame
        self.results_frame = ttk.LabelFrame(
            self.parent_frame,
            text="Search Results",
            style="WhiteText.TLabelframe"
        )
        self.results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create the treeview
        self.tree = ttk.Treeview(
            self.results_frame,
            columns=("file", "path", "keyword", "context"),
            show="headings"
        )

        # Configure columns
        self._configure_treeview_columns()

        # Add scrollbars
        self._add_scrollbars()

        # Bind double-click event
        self.tree.bind("<Double-1>", double_click_callback)

    def _configure_treeview_columns(self):
        """Configure the treeview columns."""
        # Configure column headings
        self.tree.heading("file", text="File")
        self.tree.heading("path", text="Path")
        self.tree.heading("keyword", text="Keyword")
        self.tree.heading("context", text="Context")

        # Configure column widths
        self.tree.column("file", width=200, minwidth=100)
        self.tree.column("path", width=300, minwidth=150)
        self.tree.column("keyword", width=150, minwidth=100)
        self.tree.column("context", width=400, minwidth=200)

    def _add_scrollbars(self):
        """Add scrollbars to the treeview."""
        vsb = ttk.Scrollbar(self.results_frame, orient="vertical", command=self.tree.yview)
        hsb = ttk.Scrollbar(self.results_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)

        # Pack the treeview and scrollbars
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        self.tree.pack(fill=tk.BOTH, expand=True)

    def add_result_to_tree(self, result):
        """
        Add a single result to the treeview.

        Args:
            result: SearchResult object to add

        Returns:
            str: Item ID of the inserted item
        """
        values = result.get_tree_values()
        item_id = self.tree.insert("", tk.END, values=values)
        return item_id

    def add_results_batch(self, results):
        """
        Add multiple results to the treeview efficiently.

        Args:
            results: List of SearchResult objects to add

        Returns:
            str: Item ID of the last inserted item (for scrolling)
        """
        last_item_id = None
        for result in results:
            last_item_id = self.add_result_to_tree(result)
        return last_item_id

    def clear_results(self):
        """Clear all results from the treeview."""
        for item in self.tree.get_children():
            self.tree.delete(item)

    def get_selected_result_index(self):
        """
        Get the index of the currently selected result.

        Returns:
            int: Index of selected result, or None if no selection
        """
        selection = self.tree.selection()
        if not selection:
            return None

        # Get the index of the selected item
        selected_item = selection[0]
        all_items = self.tree.get_children()

        try:
            return all_items.index(selected_item)
        except ValueError:
            return None

    def scroll_to_item(self, item_id):
        """
        Scroll to make the specified item visible.

        Args:
            item_id: ID of the item to scroll to
        """
        if item_id:
            self.tree.see(item_id)


class StatusFrameManager:
    """
    Manages the status frame with progress bars and status labels.
    """

    def __init__(self, parent_frame):
        """
        Initialize the status frame manager.

        Args:
            parent_frame: Parent frame to contain the status frame
        """
        self.parent_frame = parent_frame
        self.status_frame = None

        # Progress bars
        self.overall_progress = None
        self.file_progress = None
        self.archives_progress = None

        # Progress variables
        self.overall_percent_var = tk.StringVar()
        self.file_percent_var = tk.StringVar()
        self.archives_percent_var = tk.StringVar()

        # Status label
        self.status_var = tk.StringVar()
        self.status_label = None

        # Progress labels
        self.overall_percent = None
        self.file_percent = None
        self.archives_percent = None

    def create_status_frame(self):
        """Create the status frame with progress bars and labels."""
        # Create the status frame
        self.status_frame = ttk.LabelFrame(
            self.parent_frame,
            text="Progress",
            style="WhiteText.TLabelframe"
        )
        self.status_frame.pack(fill=tk.X, padx=5, pady=5)

        # Create progress bars
        self._create_progress_bars()

        # Create status label
        self._create_status_label()

    def _create_progress_bars(self):
        """Create the three progress bars in a single row."""
        # Create frame for progress bars
        progress_frame = ttk.Frame(self.status_frame)
        progress_frame.pack(fill=tk.X, padx=5, pady=5)

        # Overall progress (left)
        overall_frame = ttk.Frame(progress_frame)
        overall_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)

        overall_label = ttk.Label(
            overall_frame,
            text="Overall:",
            style="WhiteText.TLabel"
        )
        overall_label.pack(anchor=tk.W)

        overall_container = ttk.Frame(overall_frame)
        overall_container.pack(fill=tk.X)

        self.overall_progress = ttk.Progressbar(
            overall_container,
            mode="determinate",
            maximum=100
        )
        self.overall_progress.pack(fill=tk.X)

        self.overall_percent = tk.Label(
            overall_container,
            textvariable=self.overall_percent_var,
            font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_SMALL),
            anchor=tk.CENTER,
            background=config.UI_COLORS["tertiary_color"],
            foreground="white",
            relief="flat",
            borderwidth=0,
            padx=2,
            pady=0
        )
        self.overall_percent.place(relx=0.5, rely=0.5, anchor=tk.CENTER)

        # File progress (center)
        file_frame = ttk.Frame(progress_frame)
        file_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)

        file_label = ttk.Label(
            file_frame,
            text="File:",
            style="WhiteText.TLabel"
        )
        file_label.pack(anchor=tk.W)

        file_container = ttk.Frame(file_frame)
        file_container.pack(fill=tk.X)

        self.file_progress = ttk.Progressbar(
            file_container,
            mode="determinate",
            maximum=100
        )
        self.file_progress.pack(fill=tk.X)

        self.file_percent = tk.Label(
            file_container,
            textvariable=self.file_percent_var,
            font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_SMALL),
            anchor=tk.CENTER,
            background=config.UI_COLORS["tertiary_color"],
            foreground="white",
            relief="flat",
            borderwidth=0,
            padx=2,
            pady=0
        )
        self.file_percent.place(relx=0.5, rely=0.5, anchor=tk.CENTER)

        # Archives progress (right)
        archives_frame = ttk.Frame(progress_frame)
        archives_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)

        archives_label = ttk.Label(
            archives_frame,
            text="Archives:",
            style="WhiteText.TLabel"
        )
        archives_label.pack(anchor=tk.W)

        archives_container = ttk.Frame(archives_frame)
        archives_container.pack(fill=tk.X)

        self.archives_progress = ttk.Progressbar(
            archives_container,
            mode="determinate",
            maximum=100
        )
        self.archives_progress.pack(fill=tk.X)

        self.archives_percent = tk.Label(
            archives_container,
            textvariable=self.archives_percent_var,
            font=(config.UI_FONT_FAMILY, config.UI_FONT_SIZE_SMALL),
            anchor=tk.CENTER,
            background=config.UI_COLORS["tertiary_color"],
            foreground="white",
            relief="flat",
            borderwidth=0,
            padx=2,
            pady=0
        )
        self.archives_percent.place(relx=0.5, rely=0.5, anchor=tk.CENTER)

    def _create_status_label(self):
        """Create the status label."""
        self.status_label = ttk.Label(
            self.status_frame,
            textvariable=self.status_var,
            style="WhiteText.TLabel"
        )
        self.status_label.pack(fill=tk.X, padx=5, pady=5)

    def update_overall_progress(self, progress):
        """
        Update the overall progress bar.

        Args:
            progress (float): Progress value (0-100)
        """
        self.overall_progress["value"] = progress
        self.overall_percent_var.set(f"{int(progress)}%")
        self._update_percent_label_color(self.overall_percent, progress)

    def update_file_progress(self, progress):
        """
        Update the file progress bar.

        Args:
            progress (float): Progress value (0-100)
        """
        self.file_progress["value"] = progress
        self.file_percent_var.set(f"{int(progress)}%")
        self._update_percent_label_color(self.file_percent, progress)

    def update_archives_progress(self, progress):
        """
        Update the archives progress bar.

        Args:
            progress (float): Progress value (0-100)
        """
        self.archives_progress["value"] = progress
        self.archives_percent_var.set(f"{int(progress)}%")
        self._update_percent_label_color(self.archives_percent, progress)

    def update_status(self, status):
        """
        Update the status label.

        Args:
            status (str): Status message to display
        """
        self.status_var.set(status)

    def _update_percent_label_color(self, label, progress):
        """
        Update the color of a percentage label based on progress.

        Args:
            label: The label widget to update
            progress (float): Progress value (0-100)
        """
        if progress >= 100:
            # Completed - green background
            label.configure(background=config.UI_COLORS["success_color"])
        elif progress >= 50:
            # In progress - blue background
            label.configure(background=config.UI_COLORS["accent_color"])
        else:
            # Starting/low progress - default background
            label.configure(background=config.UI_COLORS["tertiary_color"])

    def _update_percent_label_color(self, label, progress):
        """
        Update the color of a percentage label based on progress.

        Args:
            label: The label widget to update
            progress (float): Progress value (0-100)
        """
        if progress < 30:
            bg_color = "lightcoral"
        elif progress < 70:
            bg_color = "lightyellow"
        else:
            bg_color = "lightgreen"

        label.configure(background=bg_color)

    def reset_progress_bars(self):
        """Reset all progress bars to 0."""
        self.overall_progress["value"] = 0
        self.overall_percent_var.set("0%")
        self.file_progress["value"] = 0
        self.file_percent_var.set("0%")
        self.archives_progress["value"] = 0
        self.archives_percent_var.set("0%")

        # Reset label colors
        self._update_percent_label_color(self.overall_percent, 0)
        self._update_percent_label_color(self.file_percent, 0)
        self._update_percent_label_color(self.archives_percent, 0)

    def set_progress_bar_style(self, style_name):
        """
        Set the style for all progress bars.

        Args:
            style_name (str): Style name (e.g., "TProgressbar", "Warning.TProgressbar")
        """
        self.overall_progress.configure(style=style_name)
        self.file_progress.configure(style=style_name)
        self.archives_progress.configure(style=style_name)
