2025-06-21 11:35:15,628 - DEBUG - Started periodic cleanup thread
2025-06-21 11:35:16,749 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-06-21 11:35:16,817 - INFO - win32com configured. DOC parsing support enabled.
2025-06-21 11:35:16,824 - DEBUG - antiword not available as fallback for DOC files.
2025-06-21 11:35:18,545 - DEBUG - Could not set window icon: bitmap "C:\Users\<USER>\Desktop\L457\SearchTools-v1\searchtools\ui\..\..\icon.ico" not defined
2025-06-21 11:35:19,765 - ERROR - Error applying UI styling [function=apply_styling, args_count=1, kwargs_keys=[]]
2025-06-21 11:35:19,769 - DEBUG - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\L457\SearchTools-v1\searchtools\utils\error_handling.py", line 571, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\L457\SearchTools-v1\searchtools\ui\components\ui_manager.py", line 39, in apply_styling
    self._style_progressbar()
  File "C:\Users\<USER>\Desktop\L457\SearchTools-v1\searchtools\ui\components\ui_manager.py", line 108, in _style_progressbar
    background=config.UI_COLORS["primary_color"],
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
KeyError: 'primary_color'

2025-06-21 11:35:19,769 - INFO - MainWindow initialized with component-based architecture
2025-06-21 11:35:21,547 - INFO - Match in text test0.txt [Line 1]: test
2025-06-21 11:35:21,554 - INFO - Match in text test1.txt [Line 1]: test
2025-06-21 11:35:21,554 - INFO - Match in text test2.txt [Line 1]: test
2025-06-21 11:35:21,554 - INFO - Match in text test3.txt [Line 1]: test
2025-06-21 11:35:21,557 - INFO - Match in text test4.txt [Line 1]: test
2025-06-21 11:35:21,557 - INFO - Match in text test5.txt [Line 1]: test
2025-06-21 11:35:21,557 - INFO - Match in text test6.txt [Line 1]: test
2025-06-21 11:35:21,559 - INFO - Match in text test7.txt [Line 1]: test
2025-06-21 11:35:21,561 - INFO - Match in text test8.txt [Line 1]: test
2025-06-21 11:35:21,563 - INFO - Match in text test9.txt [Line 1]: test
2025-06-21 11:35:21,565 - INFO - Match in text test10.txt [Line 1]: test
2025-06-21 11:35:21,566 - INFO - Match in text test11.txt [Line 1]: test
2025-06-21 11:35:21,566 - INFO - Match in text test12.txt [Line 1]: test
2025-06-21 11:35:21,566 - INFO - Match in text test13.txt [Line 1]: test
2025-06-21 11:35:21,566 - INFO - Match in text test14.txt [Line 1]: test
2025-06-21 11:35:21,566 - INFO - Match in text test15.txt [Line 1]: test
2025-06-21 11:35:21,566 - INFO - Match in text test16.txt [Line 1]: test
2025-06-21 11:35:21,566 - INFO - Match in text test17.txt [Line 1]: test
2025-06-21 11:35:21,566 - INFO - Match in text test18.txt [Line 1]: test
2025-06-21 11:35:21,566 - INFO - Match in text test19.txt [Line 1]: test
2025-06-21 11:35:22,605 - INFO - Match in text test.txt [Line 1]: test
2025-06-21 11:35:22,605 - INFO - Match in text test.txt [Line 1]: test
2025-06-21 11:35:23,070 - DEBUG - Cleaned up processed result IDs, kept 5000 entries
2025-06-21 11:35:23,644 - DEBUG - Overall progress updated: 2.0%
2025-06-21 11:35:23,654 - DEBUG - Overall progress updated: 100%
2025-06-21 11:35:24,068 - DEBUG - Validated extraction: normal.txt (1024 bytes)
2025-06-21 11:35:24,077 - DEBUG - Created secure temporary directory: C:\Users\<USER>\AppData\Local\Temp\searchtools_secure_rvixg8kt
2025-06-21 11:35:24,078 - DEBUG - Created temporary directory: C:\Users\<USER>\AppData\Local\Temp\searchtools_secure_rvixg8kt
2025-06-21 11:35:24,080 - WARNING - Failed to clean up temporary directory: C:\Users\<USER>\AppData\Local\Temp\searchtools_secure_rvixg8kt
2025-06-21 11:35:24,092 - INFO - rarfile configured. RAR parsing support enabled.
2025-06-21 11:35:24,205 - INFO - py7zr configured. 7z archive parsing support enabled.
2025-06-21 11:35:24,760 - DEBUG - Cleaned up temporary directory: C:\Users\<USER>\AppData\Local\Temp\searchtools_secure_rvixg8kt
2025-06-21 11:35:24,760 - INFO - Cleaned up 1 temporary directories
