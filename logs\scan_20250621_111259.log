2025-06-21 11:12:59,802 - DEBUG - Started periodic cleanup thread
2025-06-21 11:13:06,055 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-06-21 11:13:06,778 - INFO - win32com configured. DOC parsing support enabled.
2025-06-21 11:13:06,787 - DEBUG - antiword not available as fallback for DOC files.
2025-06-21 11:13:07,288 - INFO - Starting scan of /test/path
2025-06-21 11:13:07,288 - INFO - Keywords: test1, test2
2025-06-21 11:13:07,288 - INFO - Options: case_sensitive=True, use_regex=False, whole_word=False
2025-06-21 11:13:07,298 - INFO - Stop scan requested by user
2025-06-21 11:13:17,085 - DEBUG - Overall progress updated: 25%
2025-06-21 11:13:17,085 - DEBUG - File progress updated: 50%
2025-06-21 11:13:17,085 - DEBUG - Status updated: Testing
2025-06-21 11:13:17,103 - DEBUG - Overall progress updated: 50%
2025-06-21 11:13:17,104 - DEBUG - File progress updated: 75%
2025-06-21 11:13:17,104 - DEBUG - All progress indicators reset
2025-06-21 11:13:17,110 - DEBUG - File progress updated: 75% for test.txt
2025-06-21 11:13:17,117 - DEBUG - Overall progress updated: 50%
2025-06-21 11:13:17,122 - DEBUG - Status updated: Processing files...
2025-06-21 11:13:17,146 - INFO - Match in text test.txt [Line Line 1]: test
2025-06-21 11:13:17,188 - INFO - Results cleared
2025-06-21 11:13:17,613 - INFO - Cleaned up 0 temporary directories
