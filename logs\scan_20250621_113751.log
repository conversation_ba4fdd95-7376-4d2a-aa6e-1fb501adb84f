2025-06-21 11:37:51,706 - DEBUG - Started periodic cleanup thread
2025-06-21 11:37:53,808 - INFO - PyPDF2 configured. PDF parsing support enabled.
2025-06-21 11:37:53,956 - INFO - win32com configured. DOC parsing support enabled.
2025-06-21 11:37:53,970 - DEBUG - antiword not available as fallback for DOC files.
2025-06-21 11:37:54,415 - INFO - Match in text test0.txt [Line 1]: test
2025-06-21 11:37:54,417 - INFO - Match in text test1.txt [Line 1]: test
2025-06-21 11:37:54,419 - INFO - Match in text test2.txt [Line 1]: test
2025-06-21 11:37:54,421 - INFO - Match in text test3.txt [Line 1]: test
2025-06-21 11:37:54,424 - INFO - Match in text test4.txt [Line 1]: test
2025-06-21 11:37:54,427 - INFO - Match in text test5.txt [Line 1]: test
2025-06-21 11:37:54,429 - INFO - Match in text test6.txt [Line 1]: test
2025-06-21 11:37:54,430 - INFO - Match in text test7.txt [Line 1]: test
2025-06-21 11:37:54,431 - INFO - Match in text test8.txt [Line 1]: test
2025-06-21 11:37:54,434 - INFO - Match in text test9.txt [Line 1]: test
2025-06-21 11:37:54,436 - INFO - Match in text test10.txt [Line 1]: test
2025-06-21 11:37:54,438 - INFO - Match in text test11.txt [Line 1]: test
2025-06-21 11:37:54,442 - INFO - Match in text test12.txt [Line 1]: test
2025-06-21 11:37:54,444 - INFO - Match in text test13.txt [Line 1]: test
2025-06-21 11:37:54,444 - INFO - Match in text test14.txt [Line 1]: test
2025-06-21 11:37:54,444 - INFO - Match in text test15.txt [Line 1]: test
2025-06-21 11:37:54,444 - INFO - Match in text test16.txt [Line 1]: test
2025-06-21 11:37:54,444 - INFO - Match in text test17.txt [Line 1]: test
2025-06-21 11:37:54,454 - INFO - Match in text test18.txt [Line 1]: test
2025-06-21 11:37:54,455 - INFO - Match in text test19.txt [Line 1]: test
