"""
Comprehensive tests for Phase 3 implementation
Tests the UI improvements, architectural migration, and enhanced functionality
"""

import unittest
import tempfile
import os
import threading
import time
from unittest.mock import Mock, patch, MagicMock

# Set up headless testing for Tkinter
import sys
if 'pytest' in sys.modules:
    # Running under pytest, use headless mode
    os.environ['DISPLAY'] = ':99'  # For Linux

import tkinter as tk

# Import the components to test
from searchtools.ui.main_window_refactored import MainWindow
from searchtools.ui.components.ui_manager import UIStyleManager, InputFrameManager, ResultsFrameManager, StatusFrameManager
from searchtools.ui.components.result_manager import ResultManager
from searchtools.ui.components.progress_tracker import ProgressTracker
from searchtools.ui.components.scan_controller import ScanController
from searchtools.core.result import SearchResult
from searchtools.parsers.archive_parser import ArchiveParser
from searchtools.utils.security import SecurePathValidator, ArchiveSecurityManager
from searchtools.utils.temp_manager import temp_manager
import config


class TestPhase3UIImprovements(unittest.TestCase):
    """Test UI improvements implemented in Phase 3."""

    @patch('tkinter.Tk')
    def test_refactored_main_window_components(self, mock_tk):
        """Test that the refactored MainWindow has all required components."""
        # Mock the root window
        mock_root = Mock()
        mock_tk.return_value = mock_root

        # Test component imports and basic functionality
        from searchtools.ui.components.ui_manager import UIStyleManager, InputFrameManager, ResultsFrameManager, StatusFrameManager
        from searchtools.ui.components.result_manager import ResultManager
        from searchtools.ui.components.progress_tracker import ProgressTracker

        # Test that components can be imported and instantiated
        self.assertTrue(hasattr(UIStyleManager, 'apply_styling'))
        self.assertTrue(hasattr(InputFrameManager, 'create_input_frame'))
        self.assertTrue(hasattr(ResultsFrameManager, 'create_results_frame'))
        self.assertTrue(hasattr(StatusFrameManager, 'create_status_frame'))
        self.assertTrue(hasattr(ResultManager, 'add_result'))
        self.assertTrue(hasattr(ProgressTracker, 'update_overall_progress'))

    def test_ui_color_scheme_configuration(self):
        """Test that UI color scheme is properly configured."""
        # Test that color configuration exists
        self.assertIn('UI_COLORS', dir(config))
        self.assertIsInstance(config.UI_COLORS, dict)

        # Test required color keys
        required_colors = ['bg_color', 'fg_color', 'accent_color', 'secondary_color']
        for color_key in required_colors:
            self.assertIn(color_key, config.UI_COLORS)

    def test_progress_bar_configuration(self):
        """Test progress bar configuration."""
        # Test that progress bar settings exist
        self.assertTrue(hasattr(config, 'UI_FONT_FAMILY'))
        self.assertTrue(hasattr(config, 'UI_FONT_SIZE_SMALL'))
        self.assertTrue(hasattr(config, 'UI_FONT_SIZE_NORMAL'))

    def test_logo_file_exists(self):
        """Test that logo file exists."""
        # Check for icon files
        icon_files = ['icon.ico', 'icon.ico.webp']
        found_icon = False
        for icon_file in icon_files:
            if os.path.exists(icon_file):
                found_icon = True
                break

        self.assertTrue(found_icon, "No icon file found")


class TestPhase3RealTimeUpdates(unittest.TestCase):
    """Test enhanced real-time update functionality."""

    def setUp(self):
        """Set up test environment."""
        # Mock the UI components to avoid Tkinter issues
        self.mock_results_frame_manager = Mock()
        self.mock_results_frame_manager.add_results_batch.return_value = "mock_item_id"
        self.mock_results_frame_manager.scroll_to_item = Mock()

        self.mock_root = Mock()
        self.mock_root.after = Mock()
        self.mock_root.update_idletasks = Mock()

        self.result_manager = ResultManager(self.mock_results_frame_manager, self.mock_root)
    
    def test_adaptive_batch_processing(self):
        """Test that result processing adapts batch size based on queue size."""
        # Add multiple results to test batching
        for i in range(20):
            result = SearchResult(
                file=f"test{i}.txt",
                path=f"/path/to/test{i}.txt",
                keyword="test",
                location=1,
                context=f"test context {i}"
            )
            self.result_manager.add_result(result)
        
        # Process the queue
        self.result_manager._process_result_queue()
        
        # Check that results were processed
        self.assertGreater(len(self.result_manager.results), 0)
        self.assertGreater(len(self.result_manager.processed_result_ids), 0)
    
    def test_duplicate_prevention(self):
        """Test that duplicate results are prevented."""
        # Add the same result twice
        result = SearchResult(
            file="test.txt",
            path="/path/to/test.txt",
            keyword="test",
            location=1,
            context="test context"
        )
        
        self.result_manager.add_result(result)
        self.result_manager.add_result(result)  # Duplicate
        
        # Process the queue
        self.result_manager._process_result_queue()
        
        # Should only have one result
        self.assertEqual(len(self.result_manager.results), 1)
    
    def test_memory_cleanup(self):
        """Test that processed result IDs are cleaned up to prevent memory growth."""
        # Simulate many processed results
        for i in range(15000):
            self.result_manager.processed_result_ids.add(f"test_id_{i}")
        
        # Trigger cleanup
        self.result_manager._cleanup_processed_ids()
        
        # Should be reduced to 5000 or less
        self.assertLessEqual(len(self.result_manager.processed_result_ids), 5000)


class TestPhase3ProgressTracking(unittest.TestCase):
    """Test enhanced progress tracking functionality."""

    def setUp(self):
        """Set up test environment."""
        # Mock the status frame manager to avoid Tkinter issues
        self.mock_status_frame_manager = Mock()
        self.mock_status_frame_manager.update_overall_progress = Mock()
        self.mock_status_frame_manager.update_file_progress = Mock()
        self.mock_status_frame_manager.update_archives_progress = Mock()

        self.progress_tracker = ProgressTracker(self.mock_status_frame_manager)
    
    def test_progress_throttling(self):
        """Test that progress updates are throttled to prevent excessive UI updates."""
        initial_progress = self.progress_tracker.overall_progress
        
        # Small progress change (should be ignored)
        self.progress_tracker.update_overall_progress(initial_progress + 0.5)
        self.assertEqual(self.progress_tracker.overall_progress, initial_progress)
        
        # Significant progress change (should be applied)
        self.progress_tracker.update_overall_progress(initial_progress + 2.0)
        self.assertEqual(self.progress_tracker.overall_progress, initial_progress + 2.0)
        
        # Completion (should always be applied)
        self.progress_tracker.update_overall_progress(100)
        self.assertEqual(self.progress_tracker.overall_progress, 100)


class TestPhase3SecurityFeatures(unittest.TestCase):
    """Test that security features are maintained in Phase 3."""
    
    def test_path_traversal_protection(self):
        """Test path traversal protection."""
        validator = SecurePathValidator()

        # Test safe path (normalize for Windows/Unix compatibility)
        try:
            safe_path = validator.validate_path("safe/path.txt", "/base/dir")
            expected_path = os.path.normpath("safe/path.txt")
            self.assertEqual(safe_path, expected_path)
        except Exception as e:
            self.fail(f"Safe path validation failed: {e}")

        # Test dangerous path
        with self.assertRaises(Exception):
            validator.validate_path("../../../etc/passwd", "/base/dir")
    
    def test_resource_exhaustion_prevention(self):
        """Test resource exhaustion prevention."""
        security_manager = ArchiveSecurityManager()
        
        # Test normal file
        try:
            safe_path = security_manager.validate_extraction(
                "normal.txt", 1024, "/tmp"
            )
            self.assertIsNotNone(safe_path)
        except Exception as e:
            self.fail(f"Normal file validation failed: {e}")
        
        # Test oversized file
        with self.assertRaises(Exception):
            security_manager.validate_extraction(
                "huge.txt", 
                config.ARCHIVE_SECURITY["max_individual_file_size"] + 1, 
                "/tmp"
            )
    
    def test_temp_directory_cleanup(self):
        """Test temporary directory cleanup."""
        # Test context manager
        with temp_manager.temp_directory() as temp_dir:
            self.assertTrue(os.path.exists(temp_dir))
        
        # Directory should be cleaned up after context
        self.assertFalse(os.path.exists(temp_dir))


class TestPhase3ArchiveSupport(unittest.TestCase):
    """Test archive support functionality."""
    
    def test_archive_detection(self):
        """Test archive file detection."""
        # Test supported formats
        self.assertTrue(ArchiveParser.is_archive("test.zip"))
        self.assertTrue(ArchiveParser.is_archive("test.rar"))
        self.assertTrue(ArchiveParser.is_archive("test.7z"))
        
        # Test unsupported format
        self.assertFalse(ArchiveParser.is_archive("test.txt"))
    
    def test_archive_capabilities(self):
        """Test archive parsing capabilities."""
        # Check that all expected formats are supported
        supported = ArchiveParser.get_supported_extensions()
        self.assertIn('.zip', supported)
        
        # RAR and 7Z availability depends on installed dependencies
        if ArchiveParser.is_rar_available():
            self.assertIn('.rar', supported)
        
        if ArchiveParser.is_7z_available():
            self.assertIn('.7z', supported)


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
